{"globals": {"Component": "readonly", "ComponentPublicInstance": "readonly", "ComputedRef": "readonly", "DirectiveBinding": "readonly", "EffectScope": "readonly", "ExtractDefaultPropTypes": "readonly", "ExtractPropTypes": "readonly", "ExtractPublicPropTypes": "readonly", "InjectionKey": "readonly", "MaybeRef": "readonly", "MaybeRefOrGetter": "readonly", "PropType": "readonly", "Ref": "readonly", "VNode": "readonly", "WritableComputedRef": "readonly", "computed": "readonly", "createApp": "readonly", "customRef": "readonly", "defineAsyncComponent": "readonly", "defineComponent": "readonly", "effectScope": "readonly", "getCurrentInstance": "readonly", "getCurrentScope": "readonly", "h": "readonly", "inject": "readonly", "isProxy": "readonly", "isReactive": "readonly", "isReadonly": "readonly", "isRef": "readonly", "markRaw": "readonly", "nextTick": "readonly", "onActivated": "readonly", "onBeforeMount": "readonly", "onBeforeRouteLeave": "readonly", "onBeforeRouteUpdate": "readonly", "onBeforeUnmount": "readonly", "onBeforeUpdate": "readonly", "onDeactivated": "readonly", "onErrorCaptured": "readonly", "onMounted": "readonly", "onRenderTracked": "readonly", "onRenderTriggered": "readonly", "onScopeDispose": "readonly", "onServerPrefetch": "readonly", "onUnmounted": "readonly", "onUpdated": "readonly", "onWatcherCleanup": "readonly", "provide": "readonly", "reactive": "readonly", "readonly": "readonly", "ref": "readonly", "resolveComponent": "readonly", "shallowReactive": "readonly", "shallowReadonly": "readonly", "shallowRef": "readonly", "toRaw": "readonly", "toRef": "readonly", "toRefs": "readonly", "toValue": "readonly", "triggerRef": "readonly", "unref": "readonly", "useAttrs": "readonly", "useCssModule": "readonly", "useCssVars": "readonly", "useId": "readonly", "useLink": "readonly", "useModel": "readonly", "useRoute": "readonly", "useRouter": "readonly", "useSlots": "readonly", "useTemplateRef": "readonly", "watch": "readonly", "watchEffect": "readonly", "watchPostEffect": "readonly", "watchSyncEffect": "readonly"}}