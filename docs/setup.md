# Installation

## Prerequisites

### 1. (MacOS only) Install an updated `bash` shell

By default MacOS ships an outdated version of bash due to a license issue (more info [here](https://itnext.io/upgrading-bash-on-macos-7138bd1066ba#:~:text=The%20reason%20that%20Apple%20includes,about%20this%20here%20and%20here.)). In order to use the helper scripts (files: `*-tools.sh`) an up to date version of `bash` is required. To install an up to date `bash` first install the [Homebrew](https://brew.sh/) package manager and the run `brew install bash`. Make sure that this `bash` is available in your `PATH` environment variable.

### 2. Install Docker

For Ubuntu: https://docs.docker.com/engine/install/ubuntu/#install-using-the-repository
For MacOS: https://docs.docker.com/desktop/install/mac-install/

### 3. (Linux only) Add user to `docker` group (remember to logout and login to see your user member of the group)

https://docs.docker.com/engine/install/linux-postinstall/

### 4. (MacOS only) Update credential store in Docker config

Empty value of `credsStore` in `~/.docker/config.json`

```json
{
    ... other configs ...
    "credsStore": "",
    ... other configs ...
}
```

### 5. (MacOS only) Install [GPG](https://formulae.brew.sh/formula/gnupg)

### 6. Generate a [new passworded ed25519 SSH key](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent) using your work email

### 7.  Add the public key to your GitHub account.

  This key will be used to clone the repository and to connect to the staging and production servers.

### 8. Check if your ssh key is correctly configured with `ssh-add -l`

You should see your key information

### 9. (MacOS only) add key to `ssh-agent`

`ssh-agent` will "forget" this key after a reboot. To prevent this you must import your SSH keys into the keychain using this command: `ssh-add --apple-use-keychain YOUR-KEY`.

## Project setup

### 1. Clone project from Github

```
<NAME_EMAIL>:benedettocampanale/costing.git
```

### 3. Install prehook commit

```bash
ln -sf ../../git_hooks/pre-commit .git/hooks/
```

### 4. Enable dev-tools.sh autocomplete ( `bash` and `zsh` only)

```bash
. ./dev-tools.sh
```

If you're using `zsh` and you want to persist the autocompletion defs source `dev-tools.sh` with the `EXPORT_TO_AUTOLOAD` environment variable set (i.e. `EXPORT_TO_AUTOLOAD= . ./dev-tools.sh`).

Note that in order to persist the autocompletion defs these 2 conditions must be satisfied:

1. `/usr/local/share/zsh/site-functions/` folder exists and you can write in it.
2. Your shell has initialized the completion subsystem (i.e. you have `autoload -Uz compinit && compinit`
running somewhere).

If your `zsh` config caches autocomplete data make sure to clean such caches.

In order to see the available commands just run `./dev-tools.sh` without any arguments.

### 5. Build Docker containers

```bash
./dev-tools.sh docker-build
```

## Running the application

To run the application execute `./dev-tools.sh docker-up`. You should now be able to access the following services that make up the application

- [Django Admin](http://backend.costing.localhost/admin/):
  console mainly used by Costing developers.

- [Backoffice](http://backoffice.costing.localhost):
  internal app used by Costing staff to manage Costing customers.
