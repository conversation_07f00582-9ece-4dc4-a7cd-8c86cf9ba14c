#!/usr/bin/env sh

step () {
    echo -e "\n\e[1;34m[STEP]\e[0m $1..."
}

ok () {
    echo -e "\e[1;32m[OK]\e[0m $1"
}

ko () {
    echo -e "\e[1;31m[KO]\e[0m $1";
    exit 1
}

if [ "$FIX" = "1" ]; then
    FIX_STR=" --fix"
    PRETTIER_ARGS=" --write"
else
    FIX_STR=""
    PRETTIER_ARGS=" --check"
fi

lint_spa () {
    cd "$FRONTEND/$1"
    echo -e "\n\e[1;34m[= COSTING $2 LINTING =]"

    step "Running eslint$FIX_STR"
    if ! eslint --color$FIX_STR src; then
        ko "eslint found errors"
    fi
    ok "eslint"

    step "Running stylelint$FIX_STR"
    if ! stylelint$FIX_STR --quiet-deprecation-warnings src; then
        ko "stylelint found errors"
    fi
    ok "stylelint"

    step "Running prettier$PRETTIER_ARGS"
    if ! prettier $PRETTIER_ARGS src; then
        ko "prettier found errors"
    fi
    ok "prettier"
}

FRONTEND="$PWD"

lint_spa backoffice "BACKOFFICE"
lint_spa common "COMMON"
lint_spa horizon "HORIZON"
