import {useToast as usePrimeToast} from 'primevue/usetoast';

const DEFAULT_DETAIL = null;
const DEFAULT_DURATION = 3000;
const INFINITY_DURATION = Infinity;

export function useToast() {
    const toast = usePrimeToast();

    function add(text, detail = DEFAULT_DETAIL, severity = 'info', duration = DEFAULT_DURATION) {
        toast.add({
            severity,
            summary: text,
            detail,
            life: duration === INFINITY_DURATION ? null : duration,
            group: 'default',
        });
    }

    return {
        success: (text, detail, duration) => add(text, detail, 'success', duration),
        info: (text, detail, duration) => add(text, detail, 'info', duration),
        warning: (text, detail, duration) => add(text, detail, 'warn', duration),
        error: (text, detail, duration) => add(text, detail, 'error', duration),
    };
}
