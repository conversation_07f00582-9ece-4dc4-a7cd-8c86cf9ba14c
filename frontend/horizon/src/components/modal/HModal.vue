<script setup>
    import {HButton, HIcon} from '@horizon';
    import PrimeDialog from 'primevue/dialog';

    defineProps({
        closable: {
            type: Boolean,
            default: false,
        },
        closeButtonText: {
            type: String,
            default: 'Annulla',
        },
        closeButtonSemantic: {
            type: String,
            default: 'secondary',
        },
        confirmButtonText: {
            type: String,
            default: 'Conferma',
        },
        confirmButtonSemantic: {
            type: String,
            default: 'primary',
        },
        hideCloseButton: {
            type: Boolean,
            default: false,
        },
        hideConfirmButton: {
            type: Boolean,
            default: false,
        },
        subtitle: {
            type: String,
            default: null,
        },
        title: {
            type: String,
            required: true,
        },
    });

    const emit = defineEmits(['confirm']);

    const visible = defineModel('visible', {type: Boolean, default: false});

    function onConfirm() {
        emit('confirm');
    }

    function onClose() {
        visible.value = false;
    }

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeDialog
        v-model:visible="visible"
        modal
        :closable="closable"
        :breakpoints="{'99999px': '50vw', '1199px': '75vw', '575px': '90vw'}">
        <template #closeicon>
            <HIcon name="ri:close-circle-line" size="md" />
        </template>
        <template #header>
            <slot name="header">
                <h3>{{ title }}</h3>
                <h4 v-if="subtitle">{{ subtitle }}</h4>
            </slot>
        </template>
        <slot />
        <template #footer>
            <slot name="footer">
                <HButton v-if="!hideCloseButton" :semantic="closeButtonSemantic" @click="onClose">
                    {{ closeButtonText }}
                </HButton>
                <HButton v-if="!hideConfirmButton" :semantic="confirmButtonSemantic" @click="onConfirm">
                    {{ confirmButtonText }}
                </HButton>
            </slot>
        </template>
    </PrimeDialog>
</template>
