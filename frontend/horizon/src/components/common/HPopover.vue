<script setup>
    import PrimePopover from 'primevue/popover';

    import HButton from './HButton.vue';

    const popoverEl = ref(null);
    const triggerEl = ref(null);

    const popoverInterface = computed(() => ({
        toggle: event => {
            popoverEl.value.toggle(event, triggerEl.value);
        },
        show: event => popoverEl.value.show(event, triggerEl.value),
        hide: event => popoverEl.value.hide(event, triggerEl.value),
    }));

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <span>
        <span ref="triggerEl">
            <slot name="trigger" v-bind="popoverInterface">
                <HButton type="button" icon="ri:arrow-down-s-fill" @click="popoverInterface.toggle" />
            </slot>
        </span>

        <PrimePopover ref="popoverEl">
            <slot />
        </PrimePopover>
    </span>
</template>
