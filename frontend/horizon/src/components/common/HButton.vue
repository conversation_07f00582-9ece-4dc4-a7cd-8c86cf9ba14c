<script setup>
    import {HIcon} from '@horizon';
    import {createClassVariance} from '@horizon/utils/class-variance';
    import {useSlots} from 'vue';
    import PrimeButton from 'primevue/button';

    const props = defineProps({
        disabled: {
            type: Boolean,
            default: false,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        semantic: {
            type: String,
            default: 'primary',
        },
        icon: {
            type: String,
            default: null,
        },
    });

    defineEmits(['click']);
    const slots = useSlots();
    const internalDisabled = computed(() => props.loading || props.disabled);
    const internalIcon = computed(() => (props.loading ? 'ri:loader-4-line' : props.icon));
    const iconOnly = computed(() => Boolean(!slots.default && props.icon));

    const getButtonClass = createClassVariance('', {
        variants: {
            iconOnly: {
                [true]: 'p-[0.5rem]!',
            },
            semantic: {
                primary:
                    'bg-primary-500! border-primary-500! text-white! hover:bg-primary-600! hover:border-primary-600!',
                secondary:
                    'bg-secondary-500! border-secondary-500! text-white! hover:bg-secondary-600! hover:border-secondary-600!',
                warning:
                    'bg-warning-500! border-warning-500! text-white! hover:bg-warning-600! hover:border-warning-600!',
                danger: 'bg-danger-500! border-danger-500! text-white! hover:bg-danger-600! hover:border-danger-600!',
            },
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeButton
        :class="getButtonClass({...$props, iconOnly}, $attrs.class)"
        :disabled="internalDisabled"
        @click="$emit('click', $event)">
        <HIcon v-if="internalIcon" :name="internalIcon" size="sm" :spin="loading" />
        <slot />
    </PrimeButton>
</template>
