<script setup>
    import {Icon} from '@iconify/vue';
    import {createClassVariance} from '@horizon/utils/class-variance';

    const props = defineProps({
        name: {
            type: String,
            required: true,
        },
        size: {
            type: String,
            default: 'md',
        },
        spin: {
            type: Boolean,
            default: false,
        },
    });

    const iconWidth = computed(
        () =>
            ({
                sm: 16,
                md: 24,
                lg: 32,
            })[props.size],
    );

    const getIconClass = createClassVariance('inline-flex', {
        variants: {
            spin: {
                [true]: 'animation-spin',
            },
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <Icon :class="getIconClass($props, $attrs.class)" :icon="name" :width="iconWidth" :height="iconWidth" />
</template>

<style lang="scss" scoped>
    .animation-spin {
        animation-name: spin;
        animation-duration: 1200ms;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
    }
    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
</style>
