<script setup>
    import PrimeAvatar from 'primevue/avatar';

    defineProps({
        imageUrl: {
            type: String,
            required: true,
        },
        firstName: {
            type: String,
            required: true,
        },
        lastName: {
            type: String,
            required: true,
        },
    });

    const emit = defineEmits(['click']);

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeAvatar
        :label1="`${firstName[0]}${lastName[0]}`"
        :image="imageUrl"
        shape="circle"
        @click="emit('click', $event)" />
</template>
