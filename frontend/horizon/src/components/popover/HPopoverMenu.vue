<script setup>
    import HActionList from '../action/HActionList.vue';
    import HButton from '../common/HButton.vue';
    import HPopover from '../common/HPopover.vue';

    defineProps({
        icon: {
            type: String,
            default: 'ri:arrow-down-s-fill',
        },
        items: {
            type: Array,
            required: true,
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <HPopover>
        <template #trigger="{toggle}">
            <slot name="trigger" v-bind="{toggle}">
                <HButton type="button" :icon="icon" @click="toggle" />
            </slot>
        </template>
        <template #default>
            <HActionList :actions="items" />
        </template>
    </HPopover>
</template>
