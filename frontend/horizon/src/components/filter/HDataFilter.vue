<script setup>
    import {HSelect} from '@horizon';

    defineProps({
        filter: {
            type: [Object, null],
            default: null,
        },
    });
    const value = defineModel({
        type: null,
        required: true,
    });
</script>

<template>
    <template v-if="filter.type === 'choices'">
        <HSelect v-model="value" clearable editable :placeholder="filter.placeholder" :options="filter.options" />
    </template>
</template>
