<script setup>
    import {HDataFilter} from '@horizon';

    defineProps({
        filters: {
            type: Array,
            required: true,
        },
    });

    const values = defineModel({
        type: Object,
        default: () => ({}),
    });
</script>

<template>
    <div class="flex gap-2">
        <div v-for="(filter, index) in filters" :key="index" class="w-auto">
            <HDataFilter
                :model-value="values?.[filter.name]"
                :filter="filter"
                @update:model-value="value => (values[filter.name] = value)" />
        </div>
    </div>
</template>
