<script setup>
    import HTypo from '@horizon/components/typography/HTypo.vue';

    defineProps({
        title: {
            type: String,
            required: true,
        },
        subtitle: {
            type: String,
            default: null,
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <div class="flex items-center mb-4">
        <div>
            <HTypo as="h1" type="page-title">
                <slot name="title">{{ title }}</slot>
            </HTypo>
            <HTypo v-if="$slots.subtitle || subtitle" as="h2" type="page-subtitle">
                <slot name="subtitle">{{ subtitle }}</slot>
            </HTypo>
        </div>
        <div class="ml-auto flex flex-nowrap gap-4">
            <slot name="right"></slot>
        </div>
    </div>
</template>
