a
<script setup>
    import PrimeMenuBar from 'primevue/menubar';

    import HIcon from '../common/HIcon.vue';

    defineProps({
        items: {
            type: Array,
            required: true,
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeMenuBar :model="items">
        <template #start>
            <slot name="start" />
        </template>
        <template #end>
            <slot name="end" />
        </template>
        <template #item="{item, props, hasSubmenu}">
            <router-link v-if="item.route" v-slot="{href, navigate}" :to="item.route" custom>
                <a :href="href" v-bind="props.action" @click="navigate">
                    <HIcon v-if="item.icon" :name="item.icon" />
                    <span>{{ item.label }}</span>
                </a>
            </router-link>
            <a v-else :href="item.url" :target="item.target" v-bind="props.action">
                <HIcon v-if="item.icon" :name="item.icon" />
                <span>{{ item.label }}</span>
                <span v-if="hasSubmenu" class="pi pi-fw pi-angle-down" />
            </a>
        </template>
    </PrimeMenuBar>
</template>
