<script setup>
    import {useRouter} from 'vue-router';

    import HAction from './HAction.vue';

    defineProps({
        actions: {
            type: Array,
            default: () => [],
        },
    });

    const emit = defineEmits(['click']);
    const router = useRouter();

    function onActionClick(action) {
        if (action.disabled) {
            return;
        }

        if (action.route) {
            router.push(action.route);
            return;
        }

        if (action.callable) {
            action.callable();
            return;
        }

        if (action.href) {
            location.href = action.href;
            return;
        }

        emit('click', action);
    }

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <div>
        <HAction
            v-for="action in actions"
            :key="action.id"
            :disabled="action.disabled"
            :icon="action.icon"
            :semantic="action.semantic"
            @click="onActionClick(action)">
            {{ action.label }}
        </HAction>
    </div>
</template>
