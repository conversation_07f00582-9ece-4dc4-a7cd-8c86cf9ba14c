<script setup>
    import {HIcon} from '@horizon';
    import {createClassVariance} from '@horizon/utils/class-variance';

    defineProps({
        disabled: {
            type: Boolean,
            default: false,
        },
        icon: {
            type: String,
            default: null,
        },
        semantic: {
            type: String,
            default: 'secondary',
        },
    });

    defineEmits(['click']);

    const getActionClass = createClassVariance('cursor-pointer p-2 block', {
        variants: {
            disabled: {
                [true]: 'bg-gray-200 text-gray-400 cursor-not-allowed',
            },
            semantic: {
                primary: 'text-primary-500 hover:text-white hover:bg-primary-500',
                secondary: 'text-secondary-500 hover:text-white hover:bg-secondary-500',
                warning: 'text-warning-500 hover:text-white hover:bg-warning-500',
                danger: 'text-danger-500 hover:text-white hover:bg-danger-400',
            },
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <a :class="getActionClass($props)" href="javascript:void(0)" @click="$emit('click', $event)">
        <HIcon v-if="icon" class="mr-2" :name="icon" size="sm" />
        <slot />
    </a>
</template>
