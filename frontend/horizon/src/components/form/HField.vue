<script setup>
    import {HInput, HPasswordInput, HLabel, HMessage, HSelect} from '@horizon';

    const props = defineProps({
        name: {
            type: String,
            required: true,
        },
    });

    const NOT_PROVIDED = Symbol();
    const formData = inject('form-data', NOT_PROVIDED);
    const formFields = inject('form-fields', NOT_PROVIDED);
    const formStates = inject('form-states', NOT_PROVIDED);

    if (formData === NOT_PROVIDED || formFields === NOT_PROVIDED || formStates === NOT_PROVIDED) {
        throw new Error('HField must be used inside an HForm');
    }

    const field = computed(() => formFields.value[props.name]);
    const state = computed(() => formStates.value?.[props.name]);

    if (!field.value) {
        throw new Error(`Field ${props.name} not found in form`);
    }

    const fieldComponent = computed(
        () =>
            ({
                select: HSelect,
                password: HPasswordInput,
            })[field.value.type] || HInput,
    );

    const fieldProps = computed(() => {
        return {
            ...Object.keys(fieldComponent.value.props).reduce(
                (acc, key) => ({
                    ...acc,
                    [key]: field.value[key],
                }),
                {},
            ),
            name: props.name,
        };
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <HLabel v-if="field.enable" :label="field.label" :name="name">
        <component :is="fieldComponent" v-model="formData[name]" v-bind="fieldProps" />
        <HMessage v-if="state?.invalid" semantic="danger" icon="ri:error-warning-fill">
            {{ state.error.message }}
        </HMessage>
    </HLabel>
</template>
