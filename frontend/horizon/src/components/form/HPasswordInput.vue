<script setup>
    import {HIcon} from '@horizon';
    import PrimeIcon<PERSON>ield from 'primevue/iconfield';
    import PrimePassword from 'primevue/password';

    defineProps({
        name: {
            type: String,
            default: null,
        },
        icon: {
            type: String,
            default: null,
        },
        placeholder: {
            type: String,
            default: null,
        },
    });

    const model = defineModel({type: String, default: ''});

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeIconField>
        <HIcon v-if="icon" :name="icon" />
        <PrimePassword v-model="model" fluid :name="name" :placeholder="placeholder" :feedback="false" />
    </PrimeIconField>
</template>
