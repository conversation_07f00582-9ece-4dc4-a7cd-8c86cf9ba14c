<script setup>
    import {HMessage} from '@horizon';

    const props = defineProps({
        name: {
            type: String,
            required: true,
        },
    });

    const NOT_PROVIDED = Symbol();
    const formGenericErrors = inject('form-generic-errors', NOT_PROVIDED);

    if (formGenericErrors === NOT_PROVIDED) {
        throw new Error('HGenericError must be used inside an HForm');
    }

    const errorMessage = computed(() => formGenericErrors.value?.[props.name]);

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <HMessage v-if="errorMessage" semantic="danger" icon="ri:error-warning-fill">
        {{ errorMessage }}
    </HMessage>
</template>
