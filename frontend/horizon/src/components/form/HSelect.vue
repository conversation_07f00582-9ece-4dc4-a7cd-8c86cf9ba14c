<script setup>
    import PrimeSelect from 'primevue/select';

    defineProps({
        clearable: {
            type: Boolean,
            default: false,
        },
        filterable: {
            type: Boolean,
            default: false,
        },
        name: {
            type: String,
            default: null,
        },
        placeholder: {
            type: String,
            default: null,
        },
        options: {
            type: [Array, null],
            required: true,
        },
    });

    const model = defineModel({type: null, default: null});

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeSelect
        v-model="model"
        :show-clear="clearable"
        :loading="options === null"
        :filter="filterable"
        fluid
        :name="name"
        :placeholder="placeholder"
        :options="options"
        option-label="label"
        option-value="value"
        option-disabled="disabled" />
</template>
