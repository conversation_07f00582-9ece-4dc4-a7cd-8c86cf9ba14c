<script setup>
    import {Form as PrimeForm} from '@primevue/forms';
    import {zodResolver} from '@primevue/forms/resolvers/zod';
    import {z} from 'zod';

    const props = defineProps({
        fields: {
            type: Object,
            required: true,
        },
        validateOnBlur: {
            type: Boolean,
            default: false,
        },
        validateOnChange: {
            type: Boolean,
            default: false,
        },
        submitFunction: {
            type: Function,
            required: true,
        },
    });
    const model = defineModel({type: Object, default: () => ({})});

    const formRef = ref(null);
    const loading = ref(false);
    const genericErrors = ref({});

    const parsedFields = computed(() =>
        Object.entries(props.fields).reduce((acc, [key, field]) => {
            return {...acc, [key]: {...field, enable: Boolean(typeof field.enable === 'undefined' || field.enable)}};
        }, {}),
    );

    provide('form-data', model);
    provide(
        'form-fields',
        computed(() => parsedFields.value),
    );
    provide(
        'form-states',
        computed(() => formRef.value?.states),
    );
    provide(
        'form-generic-errors',
        computed(() => genericErrors.value),
    );

    function isCommonField(fieldName) {
        return fieldName.startsWith('_') || ['detail'].includes(fieldName);
    }

    function setErrors(errors) {
        Object.entries(errors).forEach(([fieldName, errorMessage]) => {
            const fieldState = formRef.value.states[fieldName];
            if (isCommonField(fieldName)) {
                genericErrors.value[fieldName] = errorMessage;
            } else if (fieldState) {
                fieldState.error = {code: 'remote', message: errorMessage};
                fieldState.errors = [fieldState.error];
                fieldState.invalid = true;
                fieldState.valid = false;
            }
        });
    }

    const resolver = computed(() =>
        zodResolver(
            z.object(
                Object.entries(parsedFields.value).reduce((acc, [key, field]) => {
                    if (field.enable && field.validation) {
                        acc[key] = field.validation;
                    }
                    return acc;
                }, {}),
            ),
        ),
    );

    const onFormSubmit = ({valid}) => {
        genericErrors.value = {};
        if (valid) {
            const submitReturn = props.submitFunction(model.value);
            if (submitReturn instanceof Promise) {
                loading.value = true;
                submitReturn
                    .finally(() => {
                        loading.value = false;
                    })
                    .catch(error => {
                        setErrors(error.response.data);
                    });
            }
        }
    };

    defineExpose({
        submit: () => formRef.value.submit(),
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeForm
        ref="formRef"
        :initial-values="model"
        :resolver="resolver"
        :validate-on-blur="validateOnBlur"
        :validate-on-value-update="validateOnChange"
        @submit="onFormSubmit">
        <slot v-bind="{submit: formRef?.submit, loading}" />
    </PrimeForm>
</template>
