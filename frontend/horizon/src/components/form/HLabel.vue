<script setup>
    import PrimeIftaLabel from 'primevue/iftalabel';

    defineProps({
        name: {
            type: String,
            default: null,
        },
        label: {
            type: String,
            default: null,
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeIftaLabel class="mb-4">
        <slot />
        <label :for="name">
            <slot name="label">
                {{ label }}
            </slot>
        </label>
    </PrimeIftaLabel>
</template>
