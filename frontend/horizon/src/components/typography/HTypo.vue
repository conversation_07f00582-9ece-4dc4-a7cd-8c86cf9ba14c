<script setup>
    import {createClassVariance} from '@horizon/utils/class-variance';

    defineProps({
        as: {
            type: String,
            default: 'span',
        },
        type: {
            type: String,
            default: 'text',
        },
    });

    const getTypoClass = createClassVariance('', {
        variants: {
            type: {
                'page-subtitle': 'block text-gray-500 text-sm',
                'page-title': 'block font-semibold text-xl mb-2',
                text: '',
            },
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <component :is="as" :class="getTypoClass($props)">
        <slot />
    </component>
</template>
