<script setup>
    import {HSelect} from '@horizon';

    defineProps({
        column: {
            type: Object,
            required: true,
        },
    });

    const emit = defineEmits(['change']);

    const model = defineModel({type: [Number, String], default: null});

    watch(model, () => {
        emit('change', model.value);
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <template v-if="column.filterChoices">
        <HSelect v-model="model" clearable :options="column.filterChoices" />
    </template>
    <template v-else>
        <input v-model="model" type="text" />
    </template>
</template>
