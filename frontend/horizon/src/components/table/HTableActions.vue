<script setup>
    import HActionList from '../action/HActionList.vue';
    import HButton from '../common/HButton.vue';
    import HPopover from '../common/HPopover.vue';

    defineProps({
        actions: {
            type: Array,
            default: () => [],
        },
    });
</script>

<template>
    <HPopover>
        <template #trigger="{toggle}">
            <HButton type="button" icon="ri:equal-fill" @click="toggle" />
        </template>
        <template #default>
            <HActionList :actions="actions" />
        </template>
    </HPopover>
</template>
