<script setup>
    defineProps({
        kind: {
            type: String,
            default: null,
        },
        value: {
            type: null,
            required: true,
        },
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <template v-if="kind === 'boolean'">{{ value ? 'yes' : 'no' }}</template>
    <template v-else>{{ value }}</template>
</template>
