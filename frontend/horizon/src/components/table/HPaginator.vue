<script setup>
    import PrimePaginator from 'primevue/paginator';

    const props = defineProps({
        perPageChoices: {
            type: Array,
            default: () => [10, 20, 30],
        },
        total: {
            type: Number,
            required: true,
        },
    });

    const perPage = defineModel('perPage', {type: Number, default: null});
    const page = defineModel('page', {type: Number, default: 1});

    watch(
        perPage,
        () => {
            if (perPage.value === null) {
                [perPage.value] = props.perPageChoices;
            }
        },
        {immediate: true},
    );

    function onPageChange(event) {
        page.value = event.page + 1;
        perPage.value = event.rows;
    }

    const primeFirst = computed(() => (page.value - 1) * perPage.value);

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimePaginator
        :first="primeFirst"
        :rows="perPage"
        :total-records="total"
        :rows-per-page-options="perPageChoices"
        @update:rows="perPage = $event"
        @page="onPageChange" />
</template>
