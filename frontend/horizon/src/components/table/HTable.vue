<script setup>
    import {HSkeleton, HTableColumnContent} from '@horizon';
    import Column from 'primevue/column';
    import PrimeDataTable from 'primevue/datatable';

    const props = defineProps({
        columns: {
            type: Array,
            required: true,
        },
        data: {
            type: Array,
            required: true,
        },
        showSkeleton: {
            type: Boolean,
            default: false,
        },
    });

    const sort = defineModel('sort', {type: Object, default: null});

    const onSortChange = event => {
        sort.value = {field: event.sortField, order: event.sortOrder};
    };

    const localData = computed(() => {
        if (props.showSkeleton) {
            return Array.from({length: 10}, (_, index) => ({id: index}));
        }
        return props.data;
    });

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeDataTable lazy :value="localData" @sort="onSortChange">
        <Column
            v-for="column in columns"
            :key="column.field"
            :class="column?.class"
            :field="column.field"
            :header="column.header"
            :show-filter-menu="false"
            :sortable="column.sortable">
            <template #body="{data: row}">
                <HSkeleton v-if="showSkeleton" />
                <slot v-else :name="`col-${column.field}-body`" v-bind="{row, column}">
                    <HTableColumnContent :kind="column.kind" :value="row[column.field]" />
                </slot>
            </template>
        </Column>
    </PrimeDataTable>
</template>
