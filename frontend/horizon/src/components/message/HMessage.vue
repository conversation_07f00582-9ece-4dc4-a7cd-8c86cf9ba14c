<script setup>
    import {HIcon} from '@horizon';
    import {createClassVariance} from '@horizon/utils/class-variance';
    import PrimeMessage from 'primevue/message';

    defineProps({
        closable: {
            type: Boolean,
            default: false,
        },
        icon: {
            type: String,
            default: null,
        },
        semantic: {
            type: String,
            default: 'info',
        },
    });

    const getMessageClass = createClassVariance('', {
        variants: {
            semantic: {
                primary: 'text-white bg-primary-500',
                secondary: 'text-white bg-secondary-500',
                warning: 'text-white bg-warning-500',
                danger: 'text-white bg-danger-400',
            },
        },
    });
    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeMessage :class="getMessageClass($props)" :closable="closable" :severity="semantic" size="small" class="my-2">
        <div class="flex items-center w-full">
            <HIcon v-if="icon" :name="icon" class="mr-2" />
            <slot />
        </div>
    </PrimeMessage>
</template>
