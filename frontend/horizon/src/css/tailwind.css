@import 'tailwindcss';

@layer theme, base, components, utilities;

@theme {
    --color-primary-50: oklch(0.977 0.013 236.62);
    --color-primary-100: oklch(0.951 0.026 236.824);
    --color-primary-200: oklch(0.901 0.058 230.902);
    --color-primary-300: oklch(0.828 0.111 230.318);
    --color-primary-400: oklch(0.746 0.16 232.661);
    --color-primary-500: oklch(0.685 0.169 237.323);
    --color-primary-600: oklch(0.588 0.158 241.966);
    --color-primary-700: oklch(0.5 0.134 242.749);
    --color-primary-800: oklch(0.443 0.11 240.79);
    --color-primary-900: oklch(0.391 0.09 240.876);
    --color-primary-950: oklch(0.293 0.066 243.157);

    --color-secondary-50: oklch(0.984 0.003 247.858);
    --color-secondary-100: oklch(0.968 0.007 247.896);
    --color-secondary-200: oklch(0.929 0.013 255.508);
    --color-secondary-300: oklch(0.869 0.022 252.894);
    --color-secondary-400: oklch(0.704 0.04 256.788);
    --color-secondary-500: oklch(0.554 0.046 257.417);
    --color-secondary-600: oklch(0.446 0.043 257.281);
    --color-secondary-700: oklch(0.372 0.044 257.287);
    --color-secondary-800: oklch(0.279 0.041 260.031);
    --color-secondary-900: oklch(0.208 0.042 265.755);
    --color-secondary-950: oklch(0.129 0.042 264.695);

    --color-warning-50: oklch(0.98 0.016 73.684);
    --color-warning-100: oklch(0.954 0.038 75.164);
    --color-warning-200: oklch(0.901 0.076 70.697);
    --color-warning-300: oklch(0.837 0.128 66.29);
    --color-warning-400: oklch(0.75 0.183 55.934);
    --color-warning-500: oklch(0.705 0.213 47.604);
    --color-warning-600: oklch(0.646 0.222 41.116);
    --color-warning-700: oklch(0.553 0.195 38.402);
    --color-warning-800: oklch(0.47 0.157 37.304);
    --color-warning-900: oklch(0.408 0.123 38.172);
    --color-warning-950: oklch(0.266 0.079 36.259);

    --color-danger-50: oklch(0.969 0.015 12.422);
    --color-danger-100: oklch(0.941 0.03 12.58);
    --color-danger-200: oklch(0.892 0.058 10.001);
    --color-danger-300: oklch(0.81 0.117 11.638);
    --color-danger-400: oklch(0.712 0.194 13.428);
    --color-danger-500: oklch(0.645 0.246 16.439);
    --color-danger-600: oklch(0.586 0.253 17.585);
    --color-danger-700: oklch(0.514 0.222 16.935);
    --color-danger-800: oklch(0.455 0.188 13.697);
    --color-danger-900: oklch(0.41 0.159 10.272);
    --color-danger-950: oklch(0.271 0.105 12.094);
}
