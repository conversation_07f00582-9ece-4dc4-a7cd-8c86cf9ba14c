<script setup>
    import {<PERSON><PERSON><PERSON><PERSON>} from '@horizon';
    import {createClassVariance} from '@horizon/utils/class-variance';

    const SIDEBAR_OPEN_FULL = 'SIDEBAR_OPEN_FULL';
    const SIDEBAR_OPEN_MINI = 'SIDEBAR_OPEN_MINI';
    const SIDEBAR_CLOSE = 'SIDEBAR_CLOSE';
    const sidebarState = ref(SIDEBAR_OPEN_FULL);

    function toggleSidebar() {
        sidebarState.value = sidebarState.value === SIDEBAR_OPEN_FULL ? SIDEBAR_CLOSE : SIDEBAR_OPEN_FULL;
    }

    const getAsideClass = createClassVariance(
        'w-full md:w-[250px] absolute top-0 left-0 h-full z-10 transition-transform transition-width duration-150 border-r-1 border-gray-200 px-4 py-4',
        {
            variants: {
                sidebarState: {
                    [SIDEBAR_OPEN_FULL]: '',
                    [SIDEBA<PERSON>_OPEN_MINI]: 'md:w-[60px]',
                    [SIDEBAR_CLOSE]: 'transform -translate-x-full',
                },
            },
        },
    );
    const getMainClass = createClassVariance('w-full md transition-padding-left duration-350 px-4 py-4', {
        variants: {
            sidebarState: {
                [SIDEBAR_OPEN_FULL]: 'md:pl-[266px]',
                [SIDEBAR_OPEN_MINI]: 'md:pl-[76px]',
                [SIDEBAR_CLOSE]: '',
            },
        },
    });

    const hamburgerIcon = computed(() => {
        return {
            [SIDEBAR_OPEN_FULL]: 'ri:menu-unfold-4-fill',
            [SIDEBAR_OPEN_MINI]: 'ri:menu-unfold-4-fill',
            [SIDEBAR_CLOSE]: 'ri:menu-unfold-3-fill',
        }[sidebarState.value];
    });
</script>

<template>
    <div class="flex flex-nowrap w-screen h-screen">
        <aside v-if="$slots.aside" :class="getAsideClass({sidebarState})">
            <div class="flex items-center">
                <slot name="logo" />
                <HButton class="ml-auto" :icon="hamburgerIcon" @click="toggleSidebar" />
            </div>
            <slot name="aside" v-bind="{state: sidebarState}" />
        </aside>
        <div :class="getMainClass({sidebarState})">
            <header class="flex items-center">
                <slot name="header" />
                <HButton v-if="sidebarState === SIDEBAR_CLOSE" :icon="hamburgerIcon" @click="toggleSidebar" />
            </header>
            <main>
                <slot name="main" />
            </main>
        </div>
    </div>
</template>
