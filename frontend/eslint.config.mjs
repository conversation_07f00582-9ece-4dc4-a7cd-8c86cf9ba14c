import {FlatCompat} from '@eslint/eslintrc';
import {fileURLToPath} from 'node:url';
import globals from 'globals';
import js from '@eslint/js';
import path from 'node:path';
import promise from 'eslint-plugin-promise';
import vue from 'eslint-plugin-vue';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all,
});

export default [
    ...compat.extends(
        'eslint:recommended',
        'plugin:promise/recommended',
        'plugin:vue/vue3-recommended',
        'plugin:jethr/recommended',
        './.eslintrc-auto-import.json',
    ),
    {
        plugins: {
            promise,
            vue,
        },

        languageOptions: {
            globals: {
                ...globals.browser,
            },

            ecmaVersion: 'latest',
            sourceType: 'module',
        },

        rules: {
            'array-bracket-spacing': 'error',
            'arrow-spacing': 'error',
            'block-spacing': 'error',
            'brace-style': 'error',
            'comma-style': 'error',
            'dot-location': ['error', 'property'],
            'dot-notation': 'error',
            eqeqeq: 'error',
            'func-call-spacing': 'error',
            indent: 'off',
            'linebreak-style': ['error', 'unix'],
            'key-spacing': 'error',
            'keyword-spacing': 'error',
            'max-attributes-per-line': 'off',
            'max-len': 'off',
            'no-confusing-arrow': 'off',
            'no-console': 'warn',
            'no-constant-condition': 'warn',

            'no-empty': [
                'error',
                {
                    allowEmptyCatch: true,
                },
            ],

            'no-implicit-coercion': 'error',
            'no-invalid-this': 'error',
            'no-lone-blocks': 'error',
            'no-lonely-if': 'error',
            'no-loop-func': 'error',
            'no-mixed-operators': 'off',
            'no-multi-assign': 'error',
            'no-multi-spaces': 'error',
            'no-multi-str': 'error',
            'no-multiple-empty-lines': 'error',
            'no-nested-ternary': 'error',
            'no-new': 'error',
            'no-new-object': 'error',
            'no-octal': 'error',
            'no-octal-escape': 'error',
            'no-param-reassign': 'error',
            'no-return-assign': 'error',
            'no-sequences': 'error',
            'no-shadow': 'error',
            'no-sparse-arrays': 'error',
            'no-tabs': 'error',
            'no-throw-literal': 'error',
            'no-trailing-spaces': 'error',
            'no-undef-init': 'error',
            'no-undefined': 'error',
            'no-unneeded-ternary': 'error',
            'no-unused-expressions': 'error',

            'no-unused-vars': [
                'error',
                {
                    destructuredArrayIgnorePattern: '^_',
                },
            ],

            'no-useless-concat': 'error',
            'no-useless-constructor': 'error',
            'no-useless-computed-key': 'error',
            'no-useless-rename': 'error',
            'no-useless-return': 'error',
            'no-use-before-define': 'error',
            'no-var': 'error',
            'no-void': 'error',
            'object-curly-spacing': 'off',
            'vue/html-self-closing': 'off',
            'object-shorthand': 'error',
            'operator-linebreak': 'error',
            'prefer-arrow-callback': 'error',
            'prefer-const': 'error',
            'prefer-destructuring': 'error',
            'prefer-object-spread': 'error',
            'prefer-regex-literals': 'error',
            'prefer-rest-params': 'error',
            'prefer-spread': 'error',
            'prefer-template': 'error',
            'promise/always-return': 'off',
            'promise/no-nesting': 'off',
            'quote-props': ['error', 'as-needed'],
            radix: 'error',
            'require-await': 'error',
            semi: ['error', 'always'],
            'space-in-parens': 'error',
            'space-unary-ops': 'error',
            'template-curly-spacing': 'error',
            'vue/array-bracket-spacing': 'error',
            'vue/arrow-spacing': 'error',
            'vue/block-spacing': 'error',
            'vue/block-tag-newline': 'error',
            'vue/brace-style': 'error',
            'vue/comma-style': 'error',
            'vue/component-api-style': 'error',
            'vue/component-name-in-template-casing': ['error', 'PascalCase'],

            'vue/component-tags-order': [
                'error',
                {
                    order: ['script', 'template', 'style'],
                },
            ],

            'vue/custom-event-name-casing': ['error', 'kebab-case'],
            'vue/define-macros-order': 'error',
            'vue/dot-location': ['error', 'property'],
            'vue/dot-notation': 'error',
            'vue/eqeqeq': 'error',
            'vue/first-attribute-linebreak': 'off',
            'vue/func-call-spacing': 'error',
            'vue/html-closing-bracket-newline': 'off',
            'vue/html-comment-content-newline': 'error',
            'vue/html-comment-content-spacing': 'error',
            'vue/html-indent': 'off',
            'vue/key-spacing': 'error',
            'vue/keyword-spacing': 'error',
            'vue/max-attributes-per-line': 'off',
            'vue/max-len': 'off',
            'vue/next-tick-style': 'error',
            'vue/no-constant-condition': 'warn',
            'vue/no-ref-object-reactivity-loss': 'off',
            'vue/no-sparse-arrays': 'error',
            'vue/no-static-inline-styles': 'warn',
            'vue/no-this-in-before-route-enter': 'error',

            'vue/no-undef-components': [
                'error',
                {
                    ignorePatterns: ['eli?(\\-\\w+)+', 'font-awesome-', 'router-.*'],
                },
            ],

            'vue/no-undef-properties': 'warn',
            'vue/no-unused-properties': 'warn',
            'vue/no-unused-refs': 'error',
            'vue/no-useless-concat': 'error',
            'vue/no-useless-mustaches': 'error',
            'vue/no-useless-v-bind': 'error',
            'vue/no-v-text': 'error',

            'vue/object-curly-newline': [
                'error',
                {
                    multiline: true,
                },
            ],

            'vue/object-curly-spacing': 'off',
            'vue/object-shorthand': 'error',
            'vue/operator-linebreak': 'error',
            'vue/padding-line-between-blocks': 'error',
            'vue/prefer-separate-static-class': 'error',
            'vue/prefer-template': 'error',
            'vue/quote-props': ['error', 'as-needed'],
            'vue/script-indent': 'off',
            'vue/space-in-parens': 'error',
            'vue/space-unary-ops': 'error',
            'vue/template-curly-spacing': 'error',
            'vue/v-for-delimiter-style': 'error',
            'wrap-iife': 'error',
            yoda: 'error',
            'vue/singleline-html-element-content-newline': 'off',
            'vue/html-quotes': 'off',
        },
    },
    {
        files: ['**/*.vue'],

        rules: {
            indent: 'off',
        },
    },
];
