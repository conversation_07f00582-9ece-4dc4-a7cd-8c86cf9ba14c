import {defineConfig} from 'vite';
import {fileURLToPath} from 'node:url';
import {visualizer} from 'rollup-plugin-visualizer';
import AutoImport from 'unplugin-auto-import/vite';
import tailwindcss from '@tailwindcss/vite';
import vue from '@vitejs/plugin-vue';

export default ({mode}) => {
    const spa = process.env.VITE_SPA;
    return defineConfig({
        envDir: `./${spa}/`,
        envPrefix: ['VITE_', 'SPA_'],
        build: {
            sourcemap: true,
        },
        resolve: {
            alias: {
                '@': fileURLToPath(new URL(`./${spa}/src`, import.meta.url)),
                '@common': fileURLToPath(new URL('./common/src', import.meta.url)),
                '@horizon': fileURLToPath(new URL('./horizon/src', import.meta.url)),
            },
        },
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: '@use "@horizon/scss/global-vars" as *;',
                },
            },
        },
        plugins: [
            AutoImport({
                imports: ['vue', 'vue-router'],
                eslintrc: {
                    enabled: true,
                    filepath: fileURLToPath(new URL('../.eslintrc-auto-import.json', import.meta.url)),
                    globalsPropValue: 'readonly',
                },
            }),
            vue(),
            tailwindcss(),
            visualizer(), // Must be the last plugin!
        ],
        server: {
            host: '0.0.0.0',
            hmr: {
                clientPort: 80,
            },
        },
    });
};
