import {createRouter, CATCH_ALL_PATH, LOGIN_ROUTE_NAME} from '@common/router.js';
import {defineAsyncComponent} from 'vue';

// Define layouts in constants to avoid <PERSON>ue<PERSON><PERSON><PERSON> from reloading them
const AuthLayout = defineAsyncComponent(() => import('@/layouts/AuthLayout.vue'));
const FullPageLayout = defineAsyncComponent(() => import('@/layouts/FullPageLayout.vue'));

const routes = [
    {
        path: '/',
        component: () => import('@/views/DashboardView.vue'),
        name: 'dashboard',
    },
    {
        path: '/login',
        component: () => import('@/views/auth/LoginView.vue'),
        name: LOGIN_ROUTE_NAME,
        meta: {requiresAuth: false, layout: AuthLayout},
    },
    {
        path: '/customer',
        component: () => import('@/views/customer/CustomerListView.vue'),
        name: 'customer-list',
    },
    {
        path: '/customer/:id',
        component: () => import('@/views/customer/CustomerDetailView.vue'),
        name: 'customer-detail',
    },
    {
        path: CATCH_ALL_PATH,
        component: () => import('@/views/errors/NotFoundView.vue'),
        name: 'not-found',
        meta: {
            requiresAuth: false,
            layout: FullPageLayout,
        },
    },
];

const router = createRouter({
    routes,
});

function pushRouteOnAuthentication(isAuthenticated) {
    if (isAuthenticated) {
        router.push({name: 'dashboard'});
    } else {
        router.push({name: LOGIN_ROUTE_NAME});
    }
}

export default router;
export {pushRouteOnAuthentication};
