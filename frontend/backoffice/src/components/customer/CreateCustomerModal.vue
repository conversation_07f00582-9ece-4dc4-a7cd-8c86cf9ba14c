<script setup>
    import {HModal, H<PERSON>ield, HForm} from '@horizon/components';
    import v from '@common/form/validator';

    const visible = defineModel('visible', {type: Boolean, default: false});

    function vatAllowed(legalForm) {
        return ['SRL', 'SPA'].includes(legalForm);
    }

    function ssnAllowed(legalForm) {
        return ['SRL', 'SPA', 'SAS'].includes(legalForm);
    }

    const formData = ref({});
    const formFields = computed(() => ({
        name: {type: 'text', label: 'Nome azienda', validation: v.string().max(8)},
        legal_form: {
            type: 'select',
            options: [
                {label: 'SRL', value: 'SRL'},
                {label: 'SPA', value: 'SPA'},
                {label: 'SAS', value: 'SAS'},
            ],
            label: 'Tipo di società',
            validation: v.legal_form(),
        },
        vat: {
            type: 'text',
            label: 'Partita IVA',
            enable: vatAllowed(formData.value.legal_form),
            validation: v.vat(),
        },
        ssn: {
            type: 'text',
            label: 'Codice Fiscale',
            enable: ssnAllowed(formData.value.legal_form),
            validation: v.ssn(),
        },
    }));

    function createCustomer(data) {
        setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('Creating company', data);
            visible.value = false;
        }, 1000);
    }

    watch(visible, value => {
        if (!value) {
            formData.value = {};
        }
    });
</script>

<template>
    <HModal
        v-model:visible="visible"
        closable
        title="Crea un nuovo cliente"
        @confirm="$refs.createCustomerForm.submit()">
        <HForm ref="createCustomerForm" v-model="formData" :fields="formFields" :submit-function="createCustomer">
            <HField name="name" />
            <HField name="legal_form" />
            <HField name="vat" />
            <HField name="ssn" />
        </HForm>
    </HModal>
</template>
