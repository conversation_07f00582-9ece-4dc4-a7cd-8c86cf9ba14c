<script setup>
    import {HModal} from '@horizon/components';

    const props = defineProps({
        company: {
            type: [Object, null],
            required: true,
        },
    });
    const visible = defineModel('visible', {type: Boolean, default: false});

    function deleteCompany() {
        setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('Deleting company', props.company);
            visible.value = false;
        }, 1000);
    }
</script>

<template>
    <HModal
        v-if="company?.can_be_deleted"
        v-model:visible="visible"
        closable
        :title="`Vuoi eliminare il cliente ${company?.name}?`"
        confirm-button-text="Elimina"
        confirm-button-semantic="danger"
        @confirm="deleteCompany">
        L'azione è irreversibile.
    </HModal>
    <HModal
        v-else
        v-model:visible="visible"
        closable
        :title="`Il cliente ${company?.name} non è eliminabile`"
        hide-confirm-button>
        <p>Non è possibile eliminare il cliente.</p>
        <p>Contattare helpdesk per assistenza.</p>
    </HModal>
</template>
