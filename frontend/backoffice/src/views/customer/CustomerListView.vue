<script setup>
    import {HButton, HDataFilters, HPageTitle, HPaginator, HSection, HTable, HTableActions} from '@horizon/components';
    import {useCustomersQuery} from '@/composables/customer.js';
    import {useRemoteList} from '@common/composables/remote-list.js';

    import CreateCustomerModal from '@/components/customer/CreateCustomerModal.vue';
    import DeleteCustomerModal from '@/components/customer/DeleteCustomerModal.vue';

    const showCreateCustomerDialog = ref(false);
    const selectedCustomerForDeletion = ref(null);
    const {filters, page, perPage, queryParams, sort} = useRemoteList();
    const {data, isLoading} = useCustomersQuery(queryParams);

    function getActions(item) {
        return [
            {label: 'Modifica', icon: 'ri:pencil-fill', route: {name: 'customer-detail', params: {id: item.id}}},
            {
                label: 'Elimina',
                icon: 'ri:delete-bin-6-line',
                semantic: 'danger',
                callable: () => {
                    selectedCustomerForDeletion.value = item;
                },
            },
        ];
    }
</script>

<template>
    <HPageTitle title="Clienti">
        <template #right>
            <HDataFilters
                v-model="filters"
                :filters="[
                    {
                        name: 'extra',
                        placeholder: 'Filtra extra',
                        type: 'choices',
                        options: [
                            {label: 'Sì', value: true},
                            {label: 'No', value: false},
                        ],
                    },
                ]" />
            <HButton icon="ri:add-circle-line" @click="showCreateCustomerDialog = true">Crea nuovo</HButton>
        </template>
    </HPageTitle>
    <HSection>
        <HTable
            v-model:sort="sort"
            :columns="[
                {field: 'name', header: 'Nome', kind: 'string', sortable: true},
                {
                    field: 'extra',
                    header: 'Extra',
                    kind: 'boolean',
                },
                {
                    class: 'w-[80px]',
                    field: 'actions',
                    header: 'Azioni',
                },
            ]"
            :data="data.results"
            :show-skeleton="isLoading">
            <template #col-name-body="{row}">
                <router-link :to="{name: 'customer-detail', params: {id: row.id}}">{{ row.name }}</router-link>
            </template>
            <template #col-actions-body="{row}">
                <HTableActions :actions="getActions(row)" />
            </template>
        </HTable>

        <HPaginator v-model:page="page" v-model:per-page="perPage" :total="data.pages" />
    </HSection>
    <CreateCustomerModal v-model:visible="showCreateCustomerDialog" />
    <DeleteCustomerModal
        :visible="Boolean(selectedCustomerForDeletion)"
        :company="selectedCustomerForDeletion"
        @update:visible="selectedCustomerForDeletion = null" />
</template>
