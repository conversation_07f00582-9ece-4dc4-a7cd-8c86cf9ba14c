<script setup>
    import {HPageTitle, HSkeleton, HSection} from '@horizon/components';
    import {useCustomerQuery} from '@/composables/customer.js';
    import {useRoute} from 'vue-router';

    const route = useRoute();
    const customerId = computed(() => parseInt(route.params.id, 10));
    const {data, isLoading} = useCustomerQuery(customerId.value);
</script>

<template>
    <template v-if="data && !isLoading">
        <HPageTitle :title="data.name" />
        <HSection>
            {{ data }}
        </HSection>
    </template>
    <template v-else>
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
    </template>
</template>
