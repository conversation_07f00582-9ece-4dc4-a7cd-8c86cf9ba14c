<script setup>
    import {<PERSON><PERSON><PERSON>on, HForm, H<PERSON>ield, HGenericError, HTypo} from '@horizon/components';
    import {useAuthStore} from '@common/stores/auth';
    import event from '@common/event';
    import v from '@common/form/validator';

    import CostingLogo from '@/components/costing/CostingLogo.vue';

    const router = useRouter();
    const {login} = useAuthStore();

    const formData = ref({
        email: '<EMAIL>',
        password: 'costing',
    });
    const formFields = computed(() => ({
        email: {type: 'text', label: 'Email', validation: v.string().min(6)},
        password: {type: 'password', label: 'Password', validation: v.string().min(6)},
    }));

    event.on('authenticated', () => {
        router.push({name: 'dashboard'});
    });
</script>

<template>
    <HForm v-model="formData" :fields="formFields" :submit-function="login">
        <template #default="{loading, submit}">
            <HTypo type="page-title">
                <CostingLogo inline />
                Login
            </HTypo>
            <HField name="email" />
            <HField name="password" />
            <HGenericError name="detail" />
            <div class="flex">
                <HButton class="ml-auto" :loading="loading" @click="submit">Login</HButton>
            </div>
        </template>
    </HForm>
</template>
