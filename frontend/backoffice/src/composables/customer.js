import {useQuery} from '@tanstack/vue-query';

// import api from '@/api';

const EMPTY_PAGINATED_RESPONSE = {
    results: [],
    count: 0,
    pages: 1,
};
const MOCK_VALUE = {
    results: [
        {id: 1, name: 'Costing', extra: true, can_be_deleted: false},
        {id: 2, name: 'Jet HR', extra: false, can_be_deleted: true},
        {id: 3, name: 'Fiscozen', extra: false, can_be_deleted: true},
    ],
    count: 3,
    pages: 1,
};

export const useCustomersQuery = queryParams => {
    return useQuery({
        queryKey: computed(() => ['customers', queryParams.value]),
        // queryFn: async () => {
        //     return (await api.company.list(queryParams.value).catch(api.end)?.data) ?? MOCK_VALUE;
        // },
        queryFn: () => {
            return MOCK_VALUE;
        },
        placeholderData: EMPTY_PAGINATED_RESPONSE,
    });
};

export const useCustomerQuery = id => {
    const _id = parseInt(id, 10);
    return useQuery({
        queryKey: computed(() => ['customer', _id]),
        // queryFn: async () => {
        //     return (await api.company.list(queryParams.value).catch(api.end)?.data) ?? MOCK_VALUE;
        // },
        queryFn: () => {
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve(MOCK_VALUE.results.find(customer => customer.id === _id));
                }, 1000);
            });
        },
        placeholderData: null,
    });
};
