<script setup>
    import {HToast} from '@horizon/components';
    import {pushRouteOnAuthentication} from '@/router';
    import {useAuthStore} from '@common/stores/auth';
    import {useToast} from '@horizon/composables/toast';
    import event from '@common/event';

    const MainLayout = defineAsyncComponent(() => import('@/layouts/MainLayout.vue'));

    // Try to authenticate the user on app load
    // On authentication or logout, move the user to the right route
    const {isAuthenticated, fetchUser} = useAuthStore();
    watch(isAuthenticated, value => {
        pushRouteOnAuthentication(value);
    });
    fetchUser();

    // Show a toast when module import fails
    const toast = useToast();
    event.on('import-module-error', () => {
        // TODO: send error to Sentry?
        toast.error(
            'Errore di sistema',
            "Si è verificato un errore e qualche parte dell'applicazione potrebbe non funzionare correttamente. Il problema verrà risolto nel minor tempo possibile, si prega di riprovare più tardi.",
            Infinity,
        );
    });
</script>

<template>
    <HToast />
    <component :is="$route.meta.layout || MainLayout" />
</template>
