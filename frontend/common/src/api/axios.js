import axios, {AxiosError} from 'axios';

export {AxiosError};
export function createAxios(baseURL) {
    const axiosInstance = axios.create({
        baseURL,
        xsrfCookieName: 'csrftoken',
        xsrfHeaderName: 'X-CSRFTOKEN',
        withCredentials: true,
        withXSRFToken: true,
    });

    axiosInstance.interceptors.request.use(
        config => {
            return config;
        },
        error => {
            return Promise.reject(error);
        },
    );

    axiosInstance.interceptors.response.use(
        response => {
            return response;
        },
        error => {
            return Promise.reject(error);
        },
    );

    return axiosInstance;
}
