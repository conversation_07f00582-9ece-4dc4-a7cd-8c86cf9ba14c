import {createAxios} from '@common/api/axios.js';
import event from '@common/event.js';

const abortControllers = {};
/**
 * Abort any pending requests with the same key and get signal from new AbortController
 *
 * @param {string} key - The key used to identify the requests to be aborted.
 * @return {AbortSignal} - The new signal that can be used to abort requests.
 */
function abortRequestsAndGetNewSignal(key) {
    if (abortControllers[key]) {
        abortControllers[key].abort();
    }
    abortControllers[key] = new AbortController();
    return abortControllers[key].signal;
}

export class BaseClient {
    axios = null;

    constructor(baseURL) {
        this.axios = createAxios(baseURL);
    }

    _parseUrl(url) {
        if (!url.endsWith('/')) {
            throw new Error(`Missing trailing slash at the end of the url: ${url}`);
        }
        return url;
    }

    _handleApiError(error) {
        if (error.response) {
            event.emit('api-error', error.response);
        }
        return Promise.reject(error);
    }

    get(url, config = {}) {
        const signal = config.abortKey && abortRequestsAndGetNewSignal(config.abortKey);
        return this.axios.get(this._parseUrl(url), {...config, signal}).catch(error => this._handleApiError(error));
    }

    post(url, data = {}, config = {}) {
        const signal = config.abortKey && abortRequestsAndGetNewSignal(config.abortKey);
        return this.axios
            .post(this._parseUrl(url), data, {...config, signal})
            .catch(error => this._handleApiError(error));
    }

    postForm(url, data = {}, config = {}) {
        const signal = config.abortKey && abortRequestsAndGetNewSignal(config.abortKey);
        return this.axios
            .postForm(this._parseUrl(url), data, {...config, signal})
            .catch(error => this._handleApiError(error));
    }

    patch(url, data = {}, config = {}) {
        const signal = config.abortKey && abortRequestsAndGetNewSignal(config.abortKey);
        return this.axios
            .patch(this._parseUrl(url), data, {...config, signal})
            .catch(error => this._handleApiError(error));
    }

    delete(url, config = {}) {
        const signal = config.abortKey && abortRequestsAndGetNewSignal(config.abortKey);
        return this.axios.delete(this._parseUrl(url), {...config, signal}).catch(error => this._handleApiError(error));
    }

    empty({paginated = false} = {}) {
        if (paginated) {
            return Promise.resolve({
                results: [],
                count: 0,
                pages: 1,
            });
        }
        return Promise.resolve([]);
    }

    create(data, config = {}) {
        return this.postForm('', data, config);
    }

    list(params, config = {}) {
        return this.get('', {...config, params});
    }

    exists(params, config = {}) {
        return this.get('exists/', {...config, params});
    }

    count(params, config = {}) {
        return this.get('count/', {...config, params});
    }

    retrieve(id, params = {}, config = {}) {
        return this.get(`${id}/`, {...config, params});
    }

    update(id, data, config = {}) {
        return this.patch(`${id}/`, data, config);
    }

    updateOrCreate(id, data, config = {}) {
        return id ? this.update(id, data, config) : this.create(data, config);
    }

    destroy(id, config = {}) {
        return this.delete(`${id}/`, config);
    }
}
