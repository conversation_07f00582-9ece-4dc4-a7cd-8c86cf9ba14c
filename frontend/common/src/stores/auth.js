import Cookies from 'js-cookie';
import api from '@/api';
import event from '@common/event';

const user = ref(null);
const userId = computed(() => user.value?.id);
const isAuthenticated = computed(() => user.value !== null);

export function useAuthStore() {
    function fetchUser() {
        return api.boUser
            .get('self/')
            .then(response => {
                user.value = response.data;
            })
            .catch(() => {
                user.value = null;
            })
            .finally(() => {
                event.emit('fetch-user', user.value);
            });
    }

    function login(credentials) {
        return api.auth
            .post('login/', credentials)
            .then(response => {
                Cookies.set('auth-token', response.data.token);
            })
            .then(fetchUser)
            .then(() => {
                event.emit('authenticated');
            });
    }

    function logout() {
        return api.auth.post('logout/').then(() => {
            user.value = null;
            event.emit('logged-out');
        });
    }

    return {
        user: readonly(user),
        userId,
        isAuthenticated,
        login,
        fetchUser,
        logout,
    };
}
