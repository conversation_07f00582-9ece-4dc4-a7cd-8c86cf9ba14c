import {createRouter as superCreateRouter, createWebHistory} from 'vue-router';
import {useAuthStore} from '@common/stores/auth';
import Cookies from 'js-cookie';
import event from '@common/event';

const IMPORT_MODULE_ERRORS = ['Failed to fetch dynamically imported module', 'Failed to load module script'];

const LOGIN_ROUTE_NAME = 'auth_login';
const CATCH_ALL_PATH = '/:pathMatch(.*)*';

function scrollBehavior() {
    const mainElement = document.querySelector('main');
    if (mainElement) {
        mainElement.scrollTop = 0;
    }
}

function routeRequiresAuth(route) {
    return route.meta.requiresAuth || typeof route.meta.requiresAuth === 'undefined';
}

function isAuthenticatedGuard(to, from, next) {
    const {isAuthenticated} = useAuthStore();
    if (routeRequiresAuth(to) && !isAuthenticated.value) {
        next({name: LOGIN_ROUTE_NAME});
    } else {
        next();
    }
}

function onError(error, to) {
    if (IMPORT_MODULE_ERRORS.every(err => error.message.includes(err))) return;

    const lastRefreshTime = Cookies.get('last_refresh_time');
    const expirationTime = 5 * 60 * 1000; // 5 minutes
    const now = new Date().getTime();
    const isTimePassed = Boolean(lastRefreshTime && now - parseInt(lastRefreshTime, 10) > expirationTime);

    if (!lastRefreshTime || isTimePassed) {
        Cookies.set('last_refresh_time', now.toString());
        window.location = to.fullPath;
    } else {
        event.emit('import-module-error', error);
    }

    throw error;
}

function createRouter(config) {
    const router = superCreateRouter({
        scrollBehavior,
        history: createWebHistory(),
        ...config,
    });
    router.onError(onError);
    router.beforeEach(isAuthenticatedGuard);
    return router;
}

export {createRouter, CATCH_ALL_PATH, LOGIN_ROUTE_NAME};
