import {VueQueryPlugin} from '@tanstack/vue-query';

export default app => {
    app.use(VueQueryPlugin, {
        enableDevtoolsV6Plugin: true,
        queryClientConfig: {
            defaultOptions: {
                queries: {
                    retry: 1,
                    staleTime: 1000 * 60 * 5,
                    gcTime: 1000 * 60 * 5,
                },
            },
        },
    });
};
