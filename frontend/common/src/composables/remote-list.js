export function useRemoteList() {
    const filters = ref({});
    const page = ref(1);
    const perPage = ref(null);
    const sort = ref({field: 'id', order: 1});

    const queryParams = computed(() => {
        return {
            ...filters.value,
            page: page.value,
            per_page: perPage.value,
            ordering: sort.value.order === 1 ? sort.value.field : `-${sort.value.field}`,
        };
    });

    return {
        filters,
        page,
        perPage,
        queryParams,
        sort,
    };
}
