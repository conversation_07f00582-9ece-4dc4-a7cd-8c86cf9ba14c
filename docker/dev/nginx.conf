upstream costing_backend {
    server costing_backend:8000;
}

server {
    listen 8080;
    server_name backend.costing.localhost;
    client_max_body_size 20M;

    location / {
        proxy_pass http://costing_backend;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
    }
}

upstream costing_backoffice {
    server costing_backoffice:5173;
}

server {
    listen 8080;
    server_name backoffice.costing.localhost;

    location / {
        proxy_pass http://costing_backoffice;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_redirect off;
    }
}
