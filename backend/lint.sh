#!/usr/bin/env sh

step () {
    echo -e "\n\e[1;34m[STEP]\e[0m $1..."
}

ok () {
    echo -e "\e[1;32m[OK]\e[0m $1"
}

ko () {
    echo -e "\e[1;31m[KO]\e[0m $1";
    exit 1
}

echo -e "\e[1;34m[= COSTING BACKEND LINTING =]"

step "Running ruff lint"
if ! ruff check .; then
    ko "ruff found errors! Try: dev-tools.sh ruff-fix"
fi
ok "lint"

step "Running ruff format"
if ! ruff format --check .; then
    ko "ruff found errors! Try: dev-tools.sh format-backend"
fi
ok "format"
