from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from common.drf import BackofficeViewSetMixin
from costing.models import BOUser

from .serializers import LoginSerializer


class AuthViewSet(BackofficeViewSetMixin, viewsets.ViewSet):
    @action(detail=False, methods=["post"], permission_classes=[AllowAny])
    def login(self, request):
        serializer = LoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = BOUser.authenticate(**serializer.validated_data)  # Try to login as user
        if user is not None:
            request.session["bo_user_id"] = user.pk
            return Response({})
        raise PermissionDenied("Credenziali di accesso errate")

    @action(detail=False, methods=["post"])
    def logout(self, request):
        try:
            del request.session["bo_user_id"]
        except KeyError:
            pass
        return Response({})
