import pytest
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient

from costing.factories.bo_user import BOUser<PERSON>lainFactory
from costing.models import FinancialStatement, MediaFile


@pytest.fixture
def bo_user(db):
    bo_user = BOUserPlainFactory(email="<EMAIL>")
    bo_user.set_password("test")
    bo_user.save(update_fields=["password_hash"])
    return bo_user


@pytest.fixture
def authenticated_client(bo_user):
    client = APIClient()
    client.force_authenticate(user=bo_user)
    return client


@pytest.fixture
def sample_file():
    content = b"This is a test file content."
    return SimpleUploadedFile("test_file.txt", content, content_type="text/plain")


@pytest.fixture
def media_file(sample_file):
    return MediaFile.objects.create(file=sample_file)


@pytest.mark.django_db
def test_media_file_create_api(authenticated_client):
    """Test creating a MediaFile via API."""
    content = b"This is a test file for API creation."
    test_file = SimpleUploadedFile("api_test.txt", content, content_type="text/plain")

    url = reverse("media_files-list")
    data = {"file": test_file, "filename": "custom_filename.txt"}

    response = authenticated_client.post(url, data, format="multipart")

    assert response.status_code == 201
    assert MediaFile.objects.count() == 1

    media_file = MediaFile.objects.first()
    response_data = response.json()

    # Check response data
    assert response_data["id"] == media_file.id
    assert response_data["filename"] == "custom_filename.txt"
    assert response_data["mime_type"] in ["text/plain", "application/octet-stream"]
    assert response_data["size"] == len(content)
    assert response_data["in_use"] is False
    assert "created_at" in response_data
    assert "modified_at" in response_data


@pytest.mark.django_db
def test_media_file_create_api_without_filename(authenticated_client):
    """Test creating a MediaFile via API without explicit filename."""
    content = b"Test content without explicit filename."
    test_file = SimpleUploadedFile("auto_filename.txt", content)

    url = reverse("media_files-list")
    data = {"file": test_file}

    response = authenticated_client.post(url, data, format="multipart")

    assert response.status_code == 201

    media_file = MediaFile.objects.first()
    response_data = response.json()

    # Filename should be auto-set from the uploaded file
    assert response_data["filename"] == "auto_filename.txt"
    assert media_file.filename == "auto_filename.txt"


@pytest.mark.django_db
def test_media_file_retrieve_api(authenticated_client, media_file):
    """Test retrieving a MediaFile via API."""
    url = reverse("media_files-detail", kwargs={"pk": media_file.pk})

    response = authenticated_client.get(url)

    assert response.status_code == 200

    response_data = response.json()
    assert response_data["id"] == media_file.id
    assert response_data["filename"] == media_file.filename
    assert response_data["mime_type"] == media_file.mime_type
    assert response_data["size"] == media_file.size
    assert response_data["in_use"] is False


@pytest.mark.django_db
def test_media_file_retrieve_api_with_in_use_annotation(
    authenticated_client, media_file
):
    """Test retrieving a MediaFile with in_use annotation."""
    # Create a FinancialStatement that references the MediaFile
    FinancialStatement.objects.create(csv_file=media_file)

    url = reverse("media_files-detail", kwargs={"pk": media_file.pk})

    response = authenticated_client.get(url)

    assert response.status_code == 200

    response_data = response.json()
    assert response_data["in_use"] is True


@pytest.mark.django_db
def test_media_file_delete_api(authenticated_client, media_file):
    """Test deleting a MediaFile via API."""
    media_file_id = media_file.id
    url = reverse("media_files-detail", kwargs={"pk": media_file_id})

    response = authenticated_client.delete(url)

    assert response.status_code == 204
    assert MediaFile.objects.filter(id=media_file_id).count() == 0


@pytest.mark.django_db
def test_media_file_api_unauthenticated():
    """Test that unauthenticated requests are rejected."""
    client = APIClient()

    # Test create
    url = reverse("media_files-list")
    response = client.post(url, {})
    assert response.status_code == 403

    # Test retrieve
    url = reverse("media_files-detail", kwargs={"pk": 1})
    response = client.get(url)
    assert response.status_code == 403

    # Test delete
    response = client.delete(url)
    assert response.status_code == 403


@pytest.mark.django_db
def test_media_file_api_nonexistent_file(authenticated_client):
    """Test retrieving and deleting non-existent MediaFile."""
    url = reverse("media_files-detail", kwargs={"pk": 999})

    # Test retrieve
    response = authenticated_client.get(url)
    assert response.status_code == 404

    # Test delete
    response = authenticated_client.delete(url)
    assert response.status_code == 404


@pytest.mark.django_db
def test_media_file_api_readonly_fields(authenticated_client):
    """Test that read-only fields cannot be modified via API."""
    content = b"Test content for readonly fields."
    test_file = SimpleUploadedFile("readonly_test.txt", content)

    url = reverse("media_files-list")
    data = {
        "file": test_file,
        "filename": "test.txt",
        "mime_type": "custom/type",  # Should be ignored
        "size": 999,  # Should be ignored
    }

    response = authenticated_client.post(url, data, format="multipart")

    assert response.status_code == 201

    media_file = MediaFile.objects.first()
    response_data = response.json()

    # Read-only fields should not be affected by the request data
    assert response_data["mime_type"] != "custom/type"
    assert response_data["size"] != 999
    assert response_data["size"] == len(content)
    assert media_file.mime_type in ["text/plain", "application/octet-stream"]
