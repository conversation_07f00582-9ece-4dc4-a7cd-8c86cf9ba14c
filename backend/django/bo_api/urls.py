from django.urls import include, path
from rest_framework import routers

from .auth.views import AuthViewSet
from .bo_user.views import BOUserViewSet
from .media_file.views import MediaFileViewSet

router = routers.DefaultRouter()

router.register(r"auth", AuthViewSet, basename="auth")
router.register(r"bo_users", BOUserViewSet, basename="bo_users")
router.register(r"media_files", MediaFileViewSet, basename="media_files")

urlpatterns = [
    path("", include(router.urls)),
]
