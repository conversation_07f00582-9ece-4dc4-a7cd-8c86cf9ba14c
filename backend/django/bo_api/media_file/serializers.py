from rest_framework import serializers

from costing.models import MediaFile


class MediaFileSerializer(serializers.ModelSerializer):
    in_use = serializers.BooleanField(read_only=True)

    class Meta:
        model = MediaFile
        fields = [
            "id",
            "filename",
            "file",
            "mime_type",
            "size",
            "in_use",
            "created_at",
            "modified_at",
        ]
        read_only_fields = [
            "mime_type",
            "size",
            "created_at",
            "modified_at",
        ]
