from rest_framework import mixins, viewsets

from common.drf import BackofficeViewSetMixin
from costing.models import MediaFile

from .serializers import MediaFileSerializer


class MediaFileViewSet(
    BackofficeViewSetMixin,
    mixins.CreateModelMixin,
    mixins.DestroyModelMixin,
    mixins.RetrieveModelMixin,
    viewsets.GenericViewSet,
):
    serializer_class = MediaFileSerializer
    ordering_fields = ["filename", "created_at", "size"]

    def get_queryset(self):
        return MediaFile.objects.all().annotate_in_use()

    def perform_create(self, serializer):
        """Override to ensure the created instance has the in_use annotation."""
        instance = serializer.save()
        # Refresh the instance with annotation for the response
        annotated_instance = self.get_queryset().get(pk=instance.pk)
        serializer.instance = annotated_instance
