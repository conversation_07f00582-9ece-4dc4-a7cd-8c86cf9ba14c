from .base import *  # noqa: F403

BACKOFFICE_BASE_URL = "http://backoffice.costing.localhost"

DEBUG = True
ALLOWED_HOSTS = ["0.0.0.0", "backend.costing.localhost"]  # noqa: S104

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "costing",
        "USER": "costing",
        "PASSWORD": "costing",
        "HOST": "costing_db",
        "PORT": "5432",
    }
}

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [BACKOFFICE_BASE_URL]
