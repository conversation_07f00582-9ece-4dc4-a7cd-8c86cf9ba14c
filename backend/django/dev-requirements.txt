# This file was autogenerated by uv via the following command:
#    uv pip compile dev-requirements.in -o dev-requirements.txt
asttokens==3.0.0
    # via stack-data
decorator==5.1.1
    # via
    #   ipdb
    #   ipython
executing==2.2.0
    # via stack-data
ipdb==0.13.13
    # via -r dev-requirements.in
ipython==8.32.0
    # via ipdb
jedi==0.19.2
    # via ipython
matplotlib-inline==0.1.7
    # via ipython
parso==0.8.4
    # via jedi
pexpect==4.9.0
    # via ipython
prompt-toolkit==3.0.50
    # via ipython
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pygments==2.19.1
    # via ipython
ruff==0.9.6
    # via -r dev-requirements.in
stack-data==0.6.3
    # via ipython
traitlets==5.14.3
    # via
    #   ipython
    #   matplotlib-inline
wcwidth==0.2.13
    # via prompt-toolkit
