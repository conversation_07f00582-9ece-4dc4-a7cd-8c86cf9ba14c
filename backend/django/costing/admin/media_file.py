from django.contrib import admin

from common.admin import BaseModelAdmin
from common.signals import handle_delete_file_signals
from costing.models import MediaFile


class InUseFilter(admin.SimpleListFilter):
    title = "In Use"
    parameter_name = "in_use"

    def lookups(self, request, model_admin):
        return (
            ("yes", "Yes"),
            ("no", "No"),
        )

    def queryset(self, request, queryset):
        # Ensure the queryset has the annotation
        queryset = queryset.annotate_in_use()

        if self.value() == "yes":
            return queryset.filter(in_use=True)
        if self.value() == "no":
            return queryset.filter(in_use=False)
        return queryset


@admin.register(MediaFile)
class MediaFileAdmin(BaseModelAdmin):
    list_display = [
        "filename",
        "mime_type",
        "size",
        "in_use_display",
        "get_related_objects_details",
        "created_at",
    ]
    list_filter = ["mime_type", InUseFilter]
    search_fields = ["filename"]
    readonly_fields = ["size", "created_at", "modified_at"]
    date_hierarchy = "created_at"

    def get_queryset(self, request):
        """Override to include the in_use annotation."""
        return super().get_queryset(request).annotate_in_use()

    @admin.display(description="In Use", boolean=True, ordering="in_use")
    def in_use_display(self, obj):
        """Display the in_use status from the annotation."""
        return getattr(obj, "in_use", False)

    @admin.display(description="Related Objects")
    def get_related_objects_details(self, obj):
        return obj.get_related_objects_details()


handle_delete_file_signals(model_class=MediaFile, field_name="file")
