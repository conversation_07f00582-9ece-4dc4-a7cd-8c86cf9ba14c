from django.contrib import admin, messages
from django.http import HttpResponseRedirect
from django.urls import path, reverse

from common.admin import BaseModelAdmin, PrettyJSONMixin
from costing.models import FinancialStatement


@admin.register(FinancialStatement)
class FinancialStatementAdmin(PrettyJSONMixin, BaseModelAdmin):
    raw_id_fields = ["pdf_file", "xbrl_file", "csv_file"]

    def get_urls(self):
        urls = super().get_urls()
        extra_urls = [
            path(
                "<int:pk>/parse_xbrl/",
                self.admin_site.admin_view(self.parse_xbrl),
                name="parse_xbrl",
            ),
            path(
                "<int:pk>/parse_csv/",
                self.admin_site.admin_view(self.parse_csv),
                name="parse_csv",
            ),
        ]
        return extra_urls + urls

    def parse_xbrl(self, request, pk):
        fs = self.get_object(request, pk)
        try:
            fs.parse_xbrl()
        except Exception as e:  # noqa: BLE001
            messages.error(request, str(e))
        else:
            messages.success(request, "File XBRL elaborato correttamente.")
        return HttpResponseRedirect(
            reverse(
                "admin:costing_financialstatement_change",
                kwargs={"object_id": fs.id},
            )
        )

    def parse_csv(self, request, pk):
        fs = self.get_object(request, pk)
        try:
            fs.parse_csv()
        except Exception as e:  # noqa: BLE001
            messages.error(request, str(e))
        else:
            messages.success(request, "File CSV elaborato correttamente.")
        return HttpResponseRedirect(
            reverse(
                "admin:costing_financialstatement_change",
                kwargs={"object_id": fs.id},
            )
        )

    def change_view(self, request, object_id, form_url="", extra_context=None):
        if request.method == "POST":
            if "_parse_xbrl" in request.POST:
                self.parse_xbrl(request=request, pk=object_id)
                return HttpResponseRedirect(request.path)
            if "_parse_csv" in request.POST:
                self.parse_csv(request=request, pk=object_id)
                return HttpResponseRedirect(request.path)
        return super().change_view(request, object_id, form_url, extra_context)
