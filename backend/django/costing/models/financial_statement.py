from django.db import models

from common.db import BaseModel
from common.xml_utils.xml_to_data import XMLToData
from costing.models.media_file import MediaFile


class FinancialStatement(BaseModel):
    pdf_file = models.ForeignKey(
        MediaFile,
        verbose_name="File PDF",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="financial_statements_as_pdf",
    )
    xbrl_file = models.ForeignKey(
        MediaFile,
        verbose_name="File XBRL",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="financial_statements_as_xbrl",
    )
    csv_file = models.ForeignKey(
        MediaFile,
        verbose_name="File CSV",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="financial_statements_as_csv",
    )
    xbrl_data = models.JSONField(
        default=None,
        blank=True,
        null=True,
    )
    csv_data = models.JSONField(
        default=None,
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = "Bilancio"
        verbose_name_plural = "Bilanci"

    def parse_xbrl(self):
        if self.xbrl_file and self.xbrl_file.file:
            # Open the file from the MediaFile instance
            self.xbrl_file.file.open()
            xml_to_data = XMLToData(
                self.xbrl_file.file.read(),
                "PCI-2018-11-04/itcc-ci-2018-11-04.xsd",
            )
            self.xbrl_file.file.close()
            self.xbrl_data = xml_to_data.data
            self.save(update_fields=["xbrl_data"])

    def parse_csv(self):
        if self.csv_file and self.csv_file.file:
            # Open the file from the MediaFile instance
            self.csv_file.file.open()
            self.csv_file.file.seek(0)
            decoded_file = self.csv_file.file.read().decode("utf-8").splitlines()
            self.csv_file.file.close()

            header_list = decoded_file[1].split(";")[:-1]  # intestazioni
            data_lines = decoded_file[2:]  # dati

            csv_data = []
            for row in data_lines:
                # Split the row by semicolon and create a dictionary
                values = row.split(";")[:-1]  # escludi l'ultima colonna
                csv_data.append(dict(zip(header_list, values)))

            self.csv_data = csv_data
            self.save(update_fields=["csv_data"])
