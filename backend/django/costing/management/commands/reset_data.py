from django.conf import settings
from django.core.management import BaseCommand, CommandError, call_command


class Command(BaseCommand):
    help = "Recreate DB, optionally generating fake data"

    def add_arguments(self, parser):
        parser.add_argument("--empty", help="Don't load any data", action="store_true")

    def handle(self, *args, **options):
        if settings.IN_PROD:
            raise CommandError("Open your eyes! You're in production...")

        self.stdout.write(self.style.NOTICE("Resetting database..."))
        call_command("reset_db", "--no-input", "--close-sessions")
        self.stdout.write(self.style.NOTICE("Applying migrations..."))
        call_command("migrate")

        if options["empty"]:
            return

        self.stdout.write(self.style.NOTICE("Loading fake data..."))
        call_command("create_fake_data")

        self.stdout.write(self.style.SUCCESS("Done.\n"))
