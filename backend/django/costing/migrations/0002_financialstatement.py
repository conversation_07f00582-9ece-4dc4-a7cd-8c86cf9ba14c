# Generated by Django 5.1.5 on 2025-04-16 02:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('costing', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialStatement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='financial_statements/', verbose_name='File PDF da Telemaco')),
                ('xbrl_file', models.FileField(blank=True, null=True, upload_to='financial_statements/', verbose_name='File XBRL')),
                ('csv_file', models.FileField(blank=True, null=True, upload_to='financial_statements/', verbose_name='File CSV')),
                ('xbrl_data', models.JSONField(blank=True, default=None, null=True)),
                ('csv_data', models.JSONField(blank=True, default=None, null=True)),
            ],
            options={
                'verbose_name': 'Bilancio',
                'verbose_name_plural': 'Bilanci',
            },
        ),
    ]
