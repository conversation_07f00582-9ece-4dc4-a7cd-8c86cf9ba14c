# Generated by Django 5.1.5 on 2025-05-02 21:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('costing', '0003_mediafile'),
    ]

    operations = [
        migrations.AlterField(
            model_name='financialstatement',
            name='csv_file',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='financial_statements_as_csv', to='costing.mediafile', verbose_name='File CSV'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='pdf_file',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='financial_statements_as_pdf', to='costing.mediafile', verbose_name='File PDF'),
        ),
        migrations.AlterField(
            model_name='financialstatement',
            name='xbrl_file',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='financial_statements_as_xbrl', to='costing.mediafile', verbose_name='File XBRL'),
        ),
    ]
