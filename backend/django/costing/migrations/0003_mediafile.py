# Generated by Django 5.1.5 on 2025-05-02 09:47

import costing.models.media_file
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('costing', '0002_financialstatement'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('file', models.FileField(max_length=1024, upload_to=costing.models.media_file.file_path, verbose_name='File')),
                ('filename', models.CharField(blank=True, max_length=255, verbose_name='Nome file')),
                ('mime_type', models.CharField(blank=True, max_length=255, verbose_name='Tipo MIME')),
                ('size', models.PositiveIntegerField(default=0, verbose_name='Dimensione (bytes)')),
            ],
            options={
                'verbose_name': 'Media file',
                'verbose_name_plural': 'Media files',
            },
        ),
    ]
