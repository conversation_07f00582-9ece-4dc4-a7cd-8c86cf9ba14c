import pytest
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient

from costing.factories.bo_user import BOUserPlainFactory
from costing.models import FinancialStatement, MediaFile


@pytest.mark.django_db
def test_create_media_file():
    # Create a simple text file
    content = b"This is a test file."
    test_file = SimpleUploadedFile("test.txt", content)

    # Create a MediaFile instance
    media_file = MediaFile.objects.create(file=test_file)

    # Check that the file was saved
    assert media_file.file
    assert media_file.filename == "test.txt"
    assert media_file.size == len(content)

    # Check that the MIME type was detected
    # This might be different depending on whether python-magic is available
    assert media_file.mime_type in ["text/plain", "application/octet-stream"]


@pytest.mark.django_db
def test_media_file_get_related_objects_details():
    """Test the get_related_objects_details method of MediaFile."""
    # Create a MediaFile instance
    content = b"This is a test file."
    test_file = SimpleUploadedFile("test.txt", content)
    media_file = MediaFile.objects.create(file=test_file)

    # Initially, the MediaFile should have no related objects
    assert media_file.get_related_objects_details() == []

    # Create a FinancialStatement that references this MediaFile
    financial_statement = FinancialStatement.objects.create(csv_file=media_file)

    # Now the MediaFile should have related objects
    related_objects = media_file.get_related_objects_details()
    assert len(related_objects) == 1
    assert related_objects[0]["related_name"] == "financial_statements_as_csv"
    assert related_objects[0]["related_pk"] == [financial_statement.id]

    # Test with multiple references
    financial_statement2 = FinancialStatement.objects.create(pdf_file=media_file)

    # MediaFile should have multiple related objects
    related_objects = media_file.get_related_objects_details()
    assert len(related_objects) == 2

    # Find the related objects by name
    csv_related = next(
        obj
        for obj in related_objects
        if obj["related_name"] == "financial_statements_as_csv"
    )
    pdf_related = next(
        obj
        for obj in related_objects
        if obj["related_name"] == "financial_statements_as_pdf"
    )

    assert csv_related["related_pk"] == [financial_statement.id]
    assert pdf_related["related_pk"] == [financial_statement2.id]

    # Delete the references
    financial_statement.delete()
    financial_statement2.delete()

    # MediaFile should no longer have related objects
    assert media_file.get_related_objects_details() == []


@pytest.mark.django_db
def test_media_file_in_use_annotation():
    """Test the in_use annotation in MediaFile queryset."""
    # Create MediaFile instances
    content = b"This is a test file."
    test_file1 = SimpleUploadedFile("test1.txt", content)
    test_file2 = SimpleUploadedFile("test2.txt", content)

    media_file1 = MediaFile.objects.create(file=test_file1)
    media_file2 = MediaFile.objects.create(file=test_file2)

    # Test queryset without annotation
    all_files = MediaFile.objects.all()
    assert all_files.count() == 2

    # Test queryset with annotation - initially no files in use
    annotated_files = MediaFile.objects.all().annotate_in_use()
    assert annotated_files.count() == 2

    # Check that annotation works
    for media_file in annotated_files:
        assert hasattr(media_file, "in_use")
        assert media_file.in_use is False

    # Create a FinancialStatement that references one MediaFile
    FinancialStatement.objects.create(csv_file=media_file1)

    # Test annotation after creating reference
    annotated_files = MediaFile.objects.all().annotate_in_use().order_by("id")

    # Verify the correct file is marked as in use
    assert annotated_files[0].id == media_file1.id
    assert annotated_files[0].in_use is True
    assert annotated_files[1].id == media_file2.id
    assert annotated_files[1].in_use is False

    # Test with multiple references
    FinancialStatement.objects.create(pdf_file=media_file1, xbrl_file=media_file2)

    # Both files should now be in use
    annotated_files = MediaFile.objects.all().annotate_in_use().order_by("id")
    for media_file in annotated_files:
        assert media_file.in_use is True


# API Test Fixtures
@pytest.fixture
def bo_user(db):
    bo_user = BOUserPlainFactory(email="<EMAIL>")
    bo_user.set_password("test")
    bo_user.save(update_fields=["password_hash"])
    return bo_user


@pytest.fixture
def authenticated_client(bo_user):
    client = APIClient()
    client.force_authenticate(user=bo_user)
    return client


@pytest.fixture
def sample_file():
    content = b"This is a test file content."
    return SimpleUploadedFile("test_file.txt", content, content_type="text/plain")


@pytest.fixture
def api_media_file(sample_file):
    return MediaFile.objects.create(file=sample_file)


# API Tests
@pytest.mark.django_db
def test_media_file_api_create(authenticated_client):
    """Test creating a MediaFile via API."""
    content = b"This is a test file for API creation."
    test_file = SimpleUploadedFile("api_test.txt", content, content_type="text/plain")

    url = reverse("media_files-list")
    data = {"file": test_file, "filename": "custom_filename.txt"}

    response = authenticated_client.post(url, data, format="multipart")

    assert response.status_code == 201
    assert MediaFile.objects.count() == 1

    media_file = MediaFile.objects.first()
    response_data = response.json()

    # Check response data
    assert response_data["id"] == media_file.id
    assert response_data["filename"] == "custom_filename.txt"
    assert response_data["mime_type"] in ["text/plain", "application/octet-stream"]
    assert response_data["size"] == len(content)
    assert response_data["in_use"] is False
    assert "created_at" in response_data
    assert "modified_at" in response_data


@pytest.mark.django_db
def test_media_file_api_retrieve(authenticated_client, api_media_file):
    """Test retrieving a MediaFile via API."""
    url = reverse("media_files-detail", kwargs={"pk": api_media_file.pk})

    response = authenticated_client.get(url)

    assert response.status_code == 200

    response_data = response.json()
    assert response_data["id"] == api_media_file.id
    assert response_data["filename"] == api_media_file.filename
    assert response_data["mime_type"] == api_media_file.mime_type
    assert response_data["size"] == api_media_file.size
    assert response_data["in_use"] is False


@pytest.mark.django_db
def test_media_file_api_delete(authenticated_client, api_media_file):
    """Test deleting a MediaFile via API."""
    media_file_id = api_media_file.id
    url = reverse("media_files-detail", kwargs={"pk": media_file_id})

    response = authenticated_client.delete(url)

    assert response.status_code == 204
    assert MediaFile.objects.filter(id=media_file_id).count() == 0


@pytest.mark.django_db
def test_media_file_api_unauthenticated():
    """Test that unauthenticated requests are rejected."""
    client = APIClient()

    # Test create
    url = reverse("media_files-list")
    response = client.post(url, {})
    assert response.status_code == 403

    # Test retrieve
    url = reverse("media_files-detail", kwargs={"pk": 1})
    response = client.get(url)
    assert response.status_code == 403

    # Test delete
    response = client.delete(url)
    assert response.status_code == 403
