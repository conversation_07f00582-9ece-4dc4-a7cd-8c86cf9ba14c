import math

import django_filters
from rest_framework import authentication
from rest_framework.filters import Ordering<PERSON>ilter
from rest_framework.pagination import PageNumberPagination


class Pagination(PageNumberPagination):
    page_size = 25
    page_size_query_param = "page_size"

    def get_paginated_response(self, data):
        response = super().get_paginated_response(data)
        response.data["pages"] = math.trunc(response.data["count"] / self.page_size) + 1
        return response


class BOUserAuthentication(authentication.SessionAuthentication):
    def authenticate(self, request):
        # self.enforce_csrf(request)   TODO:
        return request.bo_user, None


class BackofficeViewSetMixin:
    authentication_classes = [BOUserAuthentication]
    filter_backends = [
        django_filters.rest_framework.DjangoFilterBackend,
        OrderingFilter,
    ]
    pagination_class = Pagination

    def paginate_queryset(self, queryset, view=None):
        if "page" in self.request.query_params:
            return super().paginate_queryset(queryset)
        return None
