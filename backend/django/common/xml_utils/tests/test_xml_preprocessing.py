from django.test import TestCase

from ..xml_to_data import XMLToData


class XMLPreprocessingTestCase(TestCase):
    """Test cases for XMLToData.convert_html_entities_in_xml method."""

    def test_standard_entities(self):
        """Test that standard XML entities are preserved."""
        xml_content = b"""<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <item>This contains an ampersand: &amp;</item>
          <item>This contains a less than: &lt;</item>
          <item>This contains a greater than: &gt;</item>
          <item>This contains a quote: &quot;</item>
          <item>This contains an apostrophe: &apos;</item>
        </root>"""

        processed_xml = XMLToData.convert_html_entities_in_xml(xml_content)
        processed_str = processed_xml.decode("utf-8")

        # Verify standard entities are preserved (possibly converted to numeric form)
        assert "&amp;" in processed_str or "&#38;" in processed_str
        assert "&lt;" in processed_str or "&#60;" in processed_str
        assert "&gt;" in processed_str or "&#62;" in processed_str
        assert "&quot;" in processed_str or "&#34;" in processed_str
        assert "&apos;" in processed_str or "&#39;" in processed_str

    def test_html_entities(self):
        """Test that HTML entities are converted to their XML equivalents."""
        xml_content = b"""<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <item>This contains a non-breaking space: &nbsp;</item>
          <item>This contains an accented character: &eacute;</item>
          <item>This contains a euro symbol: &euro;</item>
        </root>"""

        processed_xml = XMLToData.convert_html_entities_in_xml(xml_content)
        processed_str = processed_xml.decode("utf-8")

        # Verify HTML entities are converted to their numeric equivalents
        assert "&#160;" in processed_str  # &nbsp;
        assert "&#233;" in processed_str  # &eacute;
        assert "&#8364;" in processed_str  # &euro;

    def test_undefined_entities(self):
        """Test that undefined entities are replaced with spaces."""
        xml_content = b"""<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <item>This contains an undefined entity: &undefined;</item>
          <item>This contains another undefined entity: &notdefined;</item>
        </root>"""

        processed_xml = XMLToData.convert_html_entities_in_xml(xml_content)
        processed_str = processed_xml.decode("utf-8")

        # Verify undefined entities are replaced with spaces
        assert "&undefined;" not in processed_str
        assert "&notdefined;" not in processed_str

    def test_invalid_numeric_entities(self):
        """Test that invalid numeric entities (&#0xxx;) are corrected to &#xxx;."""
        xml_content = b"""<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <item>This contains an invalid numeric entity: &#0123;</item>
        </root>"""

        processed_xml = XMLToData.convert_html_entities_in_xml(xml_content)
        processed_str = processed_xml.decode("utf-8")

        # Verify invalid numeric entities are corrected
        assert "&#0123;" not in processed_str
        assert "&#123;" in processed_str

    def test_mixed_content(self):
        """Test processing XML with a mix of different entity types."""
        xml_content = b"""<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <item>Standard entity: &amp;</item>
          <item>HTML entity: &nbsp;</item>
          <item>Undefined entity: &undefined;</item>
          <item>Invalid numeric entity: &#0123;</item>
        </root>"""

        processed_xml = XMLToData.convert_html_entities_in_xml(xml_content)
        processed_str = processed_xml.decode("utf-8")

        # Verify all entity types are handled correctly
        assert "&amp;" in processed_str or "&#38;" in processed_str
        assert "&#160;" in processed_str
        assert "&undefined;" not in processed_str
        assert "&#0123;" not in processed_str
        assert "&#123;" in processed_str
