import re

import asn1crypto.cms
import xmlschema
from django.conf import settings

# Dictionary mapping common HTML entities to their XML equivalents
HTML_ENTITIES = {
    # Spaces and formatting characters
    "nbsp": "&#160;",  # Non-breaking space
    "ensp": "&#8194;",  # En space
    "emsp": "&#8195;",  # Em space
    "thinsp": "&#8201;",  # Thin space
    # Punctuation
    "ndash": "&#8211;",  # En dash
    "mdash": "&#8212;",  # Em dash
    "lsquo": "&#8216;",  # Left single quote
    "rsquo": "&#8217;",  # Right single quote
    "sbquo": "&#8218;",  # Single low-9 quote
    "ldquo": "&#8220;",  # Left double quote
    "rdquo": "&#8221;",  # Right double quote
    "bdquo": "&#8222;",  # Double low-9 quote
    # Symbols
    "euro": "&#8364;",  # Euro
    "copy": "&#169;",  # Copyright
    "reg": "&#174;",  # Registered trademark
    "trade": "&#8482;",  # Trademark
    "deg": "&#176;",  # Degree
    "plusmn": "&#177;",  # Plus-minus
    "para": "&#182;",  # Paragraph
    "middot": "&#183;",  # Middle dot
    "amp": "&#38;",  # Ampersand
    "lt": "&#60;",  # Less than
    "gt": "&#62;",  # Greater than
    "quot": "&#34;",  # Quotation mark
    "apos": "&#39;",  # Apostrophe
    # Accented letters and special characters
    "Agrave": "&#192;",  # À
    "Aacute": "&#193;",  # Á
    "Acirc": "&#194;",  # Â
    "Atilde": "&#195;",  # Ã
    "Auml": "&#196;",  # Ä
    "Aring": "&#197;",  # Å
    "AElig": "&#198;",  # Æ
    "Ccedil": "&#199;",  # Ç
    "Egrave": "&#200;",  # È
    "Eacute": "&#201;",  # É
    "Ecirc": "&#202;",  # Ê
    "Euml": "&#203;",  # Ë
    "Igrave": "&#204;",  # Ì
    "Iacute": "&#205;",  # Í
    "Icirc": "&#206;",  # Î
    "Iuml": "&#207;",  # Ï
    "ETH": "&#208;",  # Ð
    "Ntilde": "&#209;",  # Ñ
    "Ograve": "&#210;",  # Ò
    "Oacute": "&#211;",  # Ó
    "Ocirc": "&#212;",  # Ô
    "Otilde": "&#213;",  # Õ
    "Ouml": "&#214;",  # Ö
    "Oslash": "&#216;",  # Ø
    "Ugrave": "&#217;",  # Ù
    "Uacute": "&#218;",  # Ú
    "Ucirc": "&#219;",  # Û
    "Uuml": "&#220;",  # Ü
    "Yacute": "&#221;",  # Ý
    "THORN": "&#222;",  # Þ
    "szlig": "&#223;",  # ß
    "agrave": "&#224;",  # à
    "aacute": "&#225;",  # á
    "acirc": "&#226;",  # â
    "atilde": "&#227;",  # ã
    "auml": "&#228;",  # ä
    "aring": "&#229;",  # å
    "aelig": "&#230;",  # æ
    "ccedil": "&#231;",  # ç
    "egrave": "&#232;",  # è
    "eacute": "&#233;",  # é
    "ecirc": "&#234;",  # ê
    "euml": "&#235;",  # ë
    "igrave": "&#236;",  # ì
    "iacute": "&#237;",  # í
    "icirc": "&#238;",  # î
    "iuml": "&#239;",  # ï
    "eth": "&#240;",  # ð
    "ntilde": "&#241;",  # ñ
    "ograve": "&#242;",  # ò
    "oacute": "&#243;",  # ó
    "ocirc": "&#244;",  # ô
    "otilde": "&#245;",  # õ
    "ouml": "&#246;",  # ö
    "oslash": "&#248;",  # ø
    "ugrave": "&#249;",  # ù
    "uacute": "&#250;",  # ú
    "ucirc": "&#251;",  # û
    "uuml": "&#252;",  # ü
    "yacute": "&#253;",  # ý
    "thorn": "&#254;",  # þ
    "yuml": "&#255;",  # ÿ
}


class XMLToData:
    def __init__(self, file_bytes: bytes, xsd_file_path, check_p7m: bool = False):
        self.xsd_file_path = xsd_file_path
        if check_p7m:
            p7m_file_bytes = XMLToData.extract_file_from_p7m(file_bytes)
            is_p7m = p7m_file_bytes is not None
            self.file_bytes = file_bytes if not is_p7m else p7m_file_bytes
        else:
            self.file_bytes = file_bytes

    @staticmethod
    def extract_file_from_p7m(file_bytes) -> bytes | None:
        try:
            pkcs7 = asn1crypto.cms.ContentInfo.load(file_bytes)

            # Verifica che sia un file di tipo 'signedData'
            if pkcs7["content_type"].native != "signed_data":
                raise ValueError("Il file non contiene dati firmati (signedData).")

            # Estrai il contenuto firmato
            signed_data = pkcs7["content"]
            content = signed_data["encap_content_info"]["content"]
        except:  # noqa: E722
            return None
        else:
            return content.native

    @staticmethod
    def convert_html_entities_in_xml(xml_content: bytes) -> bytes:
        """
        Converts HTML entities in XML content to their XML-compatible numeric
        equivalents.
        This ensures proper parsing of XML files that contain HTML entities not
        defined in XML standards.
        """
        # Decode the content to string for processing
        xml_str = xml_content.decode("latin-1")

        # Replace all HTML entities with their XML equivalents
        for entity, replacement in HTML_ENTITIES.items():
            xml_str = re.sub(f"&{entity};", replacement, xml_str)

        # Handle any invalid numeric entities (if present). Es: &#065 -> &#65
        xml_str = re.sub(r"&#0([0-9]+);", r"&#\1;", xml_str)

        # Handle any remaining undefined entities by replacing them with their
        # character code or a placeholder
        # This regex finds all entity references that weren't replaced above
        for match in re.finditer(r"&([a-zA-Z0-9]+);", xml_str):
            entity = match.group(1)
            # Replace with a safe placeholder (space)
            xml_str = xml_str.replace(f"&{entity};", " ")

        # Encode back to bytes
        return xml_str.encode("utf-8")

    @property
    def data(self):
        schema = xmlschema.XMLSchema(settings.XSD_ROOT / self.xsd_file_path)
        fb = self.file_bytes

        # Remove Byte Order Mark (BOM) if present
        if fb.startswith(b"\xef\xbb\xbf"):
            fb = fb[3:]

        # Convert HTML entities to XML-compatible format
        fb = self.convert_html_entities_in_xml(fb)

        return schema.to_dict(fb, decimal_type=float)
