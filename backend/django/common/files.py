from pathlib import Path

import magic

EXTENSION_MIME_TYPES_MAPPING = {
    "bmp": ("image/x-ms-bmp",),
    "csv": ("text/csv", "text/plain"),
    "ctr": ("text/plain",),
    "dat": ("text/plain", "application/dat"),
    "del": ("text/plain",),
    "doc": ("application/x-ole-storage", "application/msword"),
    "docx": (
        "application/x-ole-storage",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ),
    "heic": ("image/heic",),
    "jlr": ("text/plain",),
    "jpeg": ("image/jpeg",),
    "jpg": ("image/jpeg",),
    "mod": ("text/plain",),
    "pdf": ("application/pdf",),
    "png": ("image/png",),
    "txt": ("text/plain",),
    "uniem": ("application/zip",),
    "xls": ("application/x-ole-storage", "application/vnd.ms-excel"),
    "xlsx": (
        "application/x-ole-storage",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ),
    "xml": ("text/xml",),
    "zip": ("application/zip",),
}

SUPPORTED_EXTENSIONS = tuple(EXTENSION_MIME_TYPES_MAPPING.keys())


class FileExtensionNotAcceptedError(Exception):
    """Raised when a file extension is not in the accepted list."""


class FileWithoutExtensionError(Exception):
    """Raised when a file has no extension."""


class UnsupportedFileExtensionError(Exception):
    """Raised when a file extension is not supported."""


class FileContentAndExtensionMismatchError(Exception):
    """Raised when a file's content doesn't match its extension."""


def get_file_mimetype(file_obj) -> str:
    # Save current position
    current_position = file_obj.tell()

    # Read the first 2048 bytes for MIME detection
    file_obj.seek(0)
    mime_type = magic.from_buffer(file_obj.read(2048), mime=True)

    # Restore position
    file_obj.seek(current_position)

    return mime_type


def check_file_type(imuf, accepted_extensions=None):  # imuf means InMemoryUploadedFile
    """
    Check if a file's type matches its extension and is in the accepted list.
    """
    if accepted_extensions is None:
        accepted_extensions = SUPPORTED_EXTENSIONS
    elif unsupported_extensions := (
        set(accepted_extensions) - set(SUPPORTED_EXTENSIONS)
    ):
        raise UnsupportedFileExtensionError(f"{tuple(unsupported_extensions)}")

    try:
        file_extension = Path(imuf.name).suffix[1:].lower()  # suffix includes dot
    except IndexError as exc:
        raise FileWithoutExtensionError from exc

    if file_extension not in accepted_extensions:
        raise FileExtensionNotAcceptedError

    mime_type = get_file_mimetype(imuf)
    if mime_type not in EXTENSION_MIME_TYPES_MAPPING[file_extension]:
        raise FileContentAndExtensionMismatchError

    # Stronger protection against stored XSS attacks via svg file uploads
    if file_extension == "xml":
        current_position = imuf.tell()
        imuf.seek(0)
        file_content = imuf.read()
        imuf.seek(current_position)
        if b"<svg " in file_content:
            raise FileContentAndExtensionMismatchError
