from django.db.models.signals import post_delete, pre_save
from django.utils import timezone


# Delete file on instance delete (NOT bulk!)
def auto_delete_file_on_delete(sender, instance, field_name, model_class, **kwargs):
    instance_file_field = getattr(instance, field_name)
    if instance_file_field:
        instance_file_field.delete(save=False)


def make_auto_delete_file_on_delete_receiver(*, field_name, model_class):
    def receiver(sender, **kwargs):
        instance = kwargs.pop("instance")
        return auto_delete_file_on_delete(
            sender, instance, field_name=field_name, model_class=model_class, **kwargs
        )

    return receiver


# Delete old file on instance file change (NOT bulk!)
def auto_delete_file_on_change(sender, instance, field_name, model_class, **kwargs):
    # When loading a fixture, instance pk is already defined,
    # but DB record does not exist yet
    if instance.pk:
        try:
            old_instance = model_class.objects.get(pk=instance.pk)
        except model_class.DoesNotExist:
            pass
        else:
            old_file = getattr(old_instance, field_name)
            new_file = getattr(instance, field_name)
            if old_file and old_file != new_file:
                instance.file_last_modified_at = timezone.localtime()
                old_file.delete(save=False)
    else:
        instance.file_last_modified_at = timezone.localtime()


def make_auto_delete_file_on_change_receiver(*, field_name, model_class):
    def receiver(sender, **kwargs):
        instance = kwargs.pop("instance")
        return auto_delete_file_on_change(
            sender, instance, field_name=field_name, model_class=model_class, **kwargs
        )

    return receiver


def handle_delete_file_signals(*, model_class, field_name):
    post_delete.connect(
        receiver=make_auto_delete_file_on_delete_receiver(
            field_name=field_name, model_class=model_class
        ),
        sender=model_class,
        weak=False,
    )
    pre_save.connect(
        receiver=make_auto_delete_file_on_change_receiver(
            field_name=field_name, model_class=model_class
        ),
        sender=model_class,
        weak=False,
    )
