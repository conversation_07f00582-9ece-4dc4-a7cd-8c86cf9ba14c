from django.contrib.auth.hashers import check_password, make_password
from django.db import models
from django.utils import timezone


class BaseModel(models.Model):
    created_at = models.DateTimeField(verbose_name="Data creazione", auto_now_add=True)
    modified_at = models.DateTimeField(
        verbose_name="Data ultima modifica", auto_now=True
    )

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        # modified_at is not updated when save is called with update_fields
        if self.pk and "update_fields" in kwargs:
            kwargs["update_fields"].append("modified_at")
        super().save(*args, **kwargs)


class BaseUserModel(BaseModel):
    is_authenticated = True

    email = models.EmailField(unique=True)
    password_hash = models.CharField(max_length=255, blank=True)
    last_login = models.DateTimeField(
        verbose_name="Ultimo accesso", null=True, blank=True
    )
    is_active = models.BooleanField(verbose_name="Attivo", default=True)

    class Meta:
        abstract = True

    def __str__(self):
        return self.email

    @classmethod
    def authenticate(cls, email, password):
        try:
            user = cls.objects.get(email__iexact=email)
        except cls.DoesNotExist:
            return None
        if user.is_active and user.check_password(password):
            user.last_login = timezone.localtime()
            user.save(update_fields=["last_login"])
            return user
        return None

    def set_password(self, password) -> None:
        self.password_hash = make_password(password)

    def check_password(self, password: str) -> bool:
        return check_password(
            password,
            self.password_hash,
            lambda pwd: self.set_password(pwd),
        )
